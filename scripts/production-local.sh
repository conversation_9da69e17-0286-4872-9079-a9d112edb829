#!/bin/bash

# Production-like Local Environment Manager
# Usage: ./scripts/production-local.sh [start|stop|restart|status|logs]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a port is in use
check_port() {
    local port=$1
    if nc -z localhost $port 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Function to start the environment
start_env() {
    echo -e "${BLUE}Starting Production-like Environment...${NC}"
    
    # Check if containers are already running
    if docker ps | grep -q lockin-postgres; then
        echo -e "${YELLOW}PostgreSQL container is already running${NC}"
    else
        echo "Starting production containers..."
        docker compose -f docker-compose.production.yml up -d postgres redis
        sleep 5
    fi
    
    # Check database connection
    if docker exec lockin-postgres pg_isready -U lockin -d lockin_prod >/dev/null 2>&1; then
        echo -e "${GREEN}✓ PostgreSQL is ready${NC}"
    else
        echo -e "${RED}✗ PostgreSQL is not ready${NC}"
        exit 1
    fi
    
    # Check Redis connection
    if docker exec lockin-redis redis-cli ping >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Redis is ready${NC}"
    else
        echo -e "${RED}✗ Redis is not ready${NC}"
        exit 1
    fi
    
    # Start the application
    if check_port 3000; then
        echo -e "${YELLOW}Application is already running on port 3000${NC}"
    else
        echo "Starting application in production mode..."
        NODE_ENV=production nohup node src/server.js > logs/production/app.log 2>&1 &
        echo $! > .production.pid
        sleep 3
        
        if check_port 3000; then
            echo -e "${GREEN}✓ Application started successfully${NC}"
            echo -e "${GREEN}Access the application at: http://localhost:3000${NC}"
        else
            echo -e "${RED}✗ Failed to start application${NC}"
            exit 1
        fi
    fi
    
    echo -e "${GREEN}Production environment is ready!${NC}"
}

# Function to stop the environment
stop_env() {
    echo -e "${BLUE}Stopping Production-like Environment...${NC}"
    
    # Stop the application
    if [ -f .production.pid ]; then
        PID=$(cat .production.pid)
        if ps -p $PID > /dev/null 2>&1; then
            echo "Stopping application (PID: $PID)..."
            kill $PID
            rm .production.pid
            echo -e "${GREEN}✓ Application stopped${NC}"
        else
            echo -e "${YELLOW}Application was not running${NC}"
            rm .production.pid
        fi
    else
        echo -e "${YELLOW}No application PID file found${NC}"
    fi
    
    # Stop containers
    echo "Stopping Docker containers..."
    docker compose -f docker-compose.production.yml down postgres redis
    echo -e "${GREEN}✓ Containers stopped${NC}"
    
    echo -e "${GREEN}Production environment stopped!${NC}"
}

# Function to restart the environment
restart_env() {
    stop_env
    echo ""
    start_env
}

# Function to show status
show_status() {
    echo -e "${BLUE}Production Environment Status:${NC}"
    echo ""
    
    # Check PostgreSQL
    if docker ps | grep -q lockin-postgres; then
        echo -e "${GREEN}✓ PostgreSQL: Running${NC}"
        docker exec lockin-postgres psql -U lockin -d lockin_prod -c "SELECT COUNT(*) as user_count FROM users;" 2>/dev/null || true
    else
        echo -e "${RED}✗ PostgreSQL: Not running${NC}"
    fi
    
    echo ""
    
    # Check Redis
    if docker ps | grep -q lockin-redis; then
        echo -e "${GREEN}✓ Redis: Running${NC}"
    else
        echo -e "${RED}✗ Redis: Not running${NC}"
    fi
    
    echo ""
    
    # Check Application
    if check_port 3000; then
        echo -e "${GREEN}✓ Application: Running on port 3000${NC}"
        
        # Check health endpoint
        HEALTH=$(curl -s http://localhost:3000/health | jq -r .status 2>/dev/null || echo "unknown")
        if [ "$HEALTH" = "healthy" ]; then
            echo -e "${GREEN}  Health: Healthy${NC}"
        else
            echo -e "${YELLOW}  Health: $HEALTH${NC}"
        fi
    else
        echo -e "${RED}✗ Application: Not running${NC}"
    fi
    
    echo ""
}

# Function to show logs
show_logs() {
    echo -e "${BLUE}Application Logs:${NC}"
    
    if [ -f logs/production/app.log ]; then
        tail -f logs/production/app.log
    else
        echo -e "${YELLOW}No application logs found${NC}"
        echo "Checking container logs..."
        docker compose -f docker-compose.production.yml logs -f
    fi
}

# Main script logic
case "$1" in
    start)
        start_env
        ;;
    stop)
        stop_env
        ;;
    restart)
        restart_env
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the production environment"
        echo "  stop    - Stop the production environment"
        echo "  restart - Restart the production environment"
        echo "  status  - Show status of all services"
        echo "  logs    - Show application logs (tail -f)"
        exit 1
        ;;
esac