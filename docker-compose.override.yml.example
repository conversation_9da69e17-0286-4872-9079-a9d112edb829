# Docker Compose Override Example
# Copy this file to docker-compose.override.yml for local customizations
# docker-compose.override.yml is git-ignored and will be automatically loaded
# This allows you to override settings without modifying the main docker-compose.yml

version: '3.8'

services:
  # Example: Override app ports or add environment variables
  app:
    ports:
      - "3001:3000"  # Use different host port
    environment:
      # Add local environment variables
      DEBUG: "true"
      LOG_LEVEL: "debug"
    # volumes:
    #   - ./custom-config:/app/config  # Add custom volume mounts

  # Example: Use different PostgreSQL port
  postgres:
    ports:
      - "5433:5432"  # Use different host port
    # environment:
    #   POSTGRES_PASSWORD: localpassword  # Override password

  # Example: Use different Redis port
  redis:
    ports:
      - "6380:6379"  # Use different host port

  # Example: Always include pgadmin without --profile flag
  # pgadmin:
  #   profiles: []  # Remove profile requirement

  # Example: Add local testing service
  # test-db:
  #   image: postgres:15-alpine
  #   container_name: lockin-test-db
  #   environment:
  #     POSTGRES_DB: lockin_test
  #     POSTGRES_USER: test
  #     POSTGRES_PASSWORD: test
  #   ports:
  #     - "5434:5432"
  #   networks:
  #     - lockin-network