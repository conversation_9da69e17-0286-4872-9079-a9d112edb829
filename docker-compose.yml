# Development Docker Compose Configuration
# Usage: docker-compose up
# This configuration is for local development with hot-reload and debugging

version: '3.8'

services:
  # Node.js Application - Development mode with nodemon
  app:
    build:
      context: .
      dockerfile: docker/app/Dockerfile.dev
    container_name: lockin-app-dev
    restart: unless-stopped
    ports:
      - "${PORT:-3000}:3000"
      - "9229:9229"  # Node.js debugging port
    environment:
      NODE_ENV: development
      PORT: 3000
      DATABASE_URL: postgresql://${POSTGRES_USER:-lockin}:${POSTGRES_PASSWORD:-devpass}@postgres:5432/${POSTGRES_DB:-lockin_dev}
      REDIS_URL: redis://redis:6379
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_PHONE_NUMBER: ${TWILIO_PHONE_NUMBER}
      THRIVECART_SECRET: ${THRIVECART_SECRET}
      EMAIL_FROM: ${EMAIL_FROM}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      LOG_LEVEL: ${LOG_LEVEL:-debug}
      LOG_DIR: /app/logs
    volumes:
      - ./src:/app/src:cached
      - ./package.json:/app/package.json:ro
      - ./package-lock.json:/app/package-lock.json:ro
      - app-logs:/app/logs
      - node-modules:/app/node_modules
    networks:
      - lockin-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  # PostgreSQL Database - Development
  postgres:
    image: postgres:15-alpine
    container_name: lockin-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-lockin_dev}
      POSTGRES_USER: ${POSTGRES_USER:-lockin}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-devpass}
      POSTGRES_HOST_AUTH_METHOD: trust  # Less strict for development
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./src/db/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./src/db/seed.sql:/docker-entrypoint-initdb.d/02-seed.sql:ro
    ports:
      - "5432:5432"
    networks:
      - lockin-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-lockin} -d ${POSTGRES_DB:-lockin_dev}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache - Development
  redis:
    image: redis:7-alpine
    container_name: lockin-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - lockin-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PgAdmin - Database management UI (development only)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: lockin-pgadmin-dev
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "5050:80"
    networks:
      - lockin-network
    depends_on:
      - postgres
    profiles:
      - tools  # Only starts when explicitly requested with --profile tools

  # Mailhog - Email testing (development only)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: lockin-mailhog-dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI
    networks:
      - lockin-network
    profiles:
      - tools  # Only starts when explicitly requested with --profile tools

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  app-logs:
    driver: local
  node-modules:
    driver: local

networks:
  lockin-network:
    driver: bridge