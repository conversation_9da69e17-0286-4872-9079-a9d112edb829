# Docker Infrastructure - Lock In Habit Tracker

This directory contains all Docker-related configuration files for the Lock In WhatsApp Habit Tracker application.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                     Docker Network: lockin_app-network       │
├─────────────────────────────────────────────────────────────┤
│                                                               │
│  ┌──────────────┐        ┌──────────────┐                   │
│  │    Nginx     │◄──────►│   Node.js    │                   │
│  │  Port 80/443 │        │  Port 3000   │                   │
│  └──────────────┘        └──────────────┘                   │
│         ▲                       │ │                          │
│    External Access              ▼ ▼                          │
│                          ┌──────────────┐                   │
│                          │  PostgreSQL  │                   │
│                          │  Port 5432   │                   │
│                          └──────────────┘                   │
│                                 ▲                            │
│                          ┌──────────────┐                   │
│                          │    Redis     │                   │
│                          │  Port 6379   │                   │
│                          └──────────────┘                   │
└─────────────────────────────────────────────────────────────┘
```

## Directory Structure

```
docker/
├── app/                    # Application Docker configurations
│   ├── Dockerfile         # Production multi-stage build
│   └── Dockerfile.dev     # Development Dockerfile with hot reload
├── nginx/                 # Nginx reverse proxy configuration
│   ├── nginx.conf        # Main Nginx configuration
│   └── ssl/              # SSL certificates
│       └── generate-self-signed.sh  # Script to generate dev certificates
├── postgres/              # PostgreSQL configuration
│   └── init/             # Database initialization scripts
│       └── 01-schema.sql # Production database schema
└── monitoring/            # Monitoring stack configuration
    ├── prometheus.yml    # Prometheus configuration
    └── grafana/          # Grafana dashboards and datasources
        ├── dashboards/
        └── datasources/
```

## Usage

### Development
```bash
# Uses docker-compose.yml with Dockerfile.dev
docker-compose up
```

### Production
```bash
# Uses docker-compose.production.yml with production Dockerfile
docker-compose -f docker-compose.production.yml up
```

### Production with Monitoring
```bash
# Includes Prometheus, Grafana, and exporters
docker-compose -f docker-compose.production.yml -f docker-compose.monitoring.yml up
```

## Configuration Files

- **app/Dockerfile**: Production-optimized multi-stage build with security hardening
- **app/Dockerfile.dev**: Development build with nodemon and volume mounts
- **nginx/nginx.conf**: Production Nginx config with SSL, rate limiting, and caching
- **postgres/init/01-schema.sql**: Complete database schema with partitioning
- **monitoring/**: Prometheus and Grafana configurations for metrics

## SSL Certificates

For development, generate self-signed certificates:
```bash
cd docker/nginx/ssl
bash generate-self-signed.sh localhost
```

For production, replace with real certificates from Let's Encrypt or your CA.