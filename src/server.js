// Load environment configuration
require('dotenv').config();

const logger = require('./config/logger');
const sessionManager = require('./services/sessionManager');
const paymentService = require('./services/paymentService');
const emailService = require('./services/emailService');
const enhancedRetentionService = require('./services/enhancedRetentionService');
const app = require('./app');

// Load production config if in production mode
const config = process.env.NODE_ENV === 'production' 
  ? require('./config/production') 
  : { server: { port: process.env.PORT || 3000, host: '0.0.0.0' } };

const PORT = config.server?.port || process.env.PORT || 3000;
const HOST = config.server?.host || '0.0.0.0';

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`Received ${signal}, starting graceful shutdown...`);
  
  // Stop services
  sessionManager.stop();
  enhancedRetentionService.stop();
  
  // Close database connections
  const pool = require('./db/connection');
  await pool.end();
  
  // Stop server
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
  
  // Force shutdown after 10 seconds
  setTimeout(() => {
    logger.error('Forced shutdown after timeout');
    process.exit(1);
  }, 10000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', { error: error.message, stack: error.stack });
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection', { reason, promise });
});

// Start server
const server = app.listen(PORT, HOST, () => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  logger.info(`WhatsApp Habit Tracker server running on http://${HOST}:${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
  
  if (isProduction) {
    logger.info('Production mode enabled with enhanced security and performance settings');
  }
  
  // Start session manager
  sessionManager.start();
  
  // Start payment services
  paymentService.startSubscriptionChecker();
  emailService.startQueueProcessor();
  
  // Start enhanced data retention service
  enhancedRetentionService.start();
  
  // Log important configuration
  logger.info('Configuration loaded', {
    environment: process.env.NODE_ENV || 'development',
    database: process.env.DATABASE_URL ? 'Connected' : 'Not configured',
    twilio: process.env.TWILIO_AUTH_TOKEN ? 'Configured' : 'Not configured',
    redis: isProduction && config.redis?.enabled ? 'Enabled' : 'Disabled',
    paymentTestMode: process.env.PAYMENT_TEST_MODE === 'true',
    sessionTimeout: '30 minutes',
    rateLimit: isProduction ? config.rateLimiting?.max + ' requests per ' + (config.rateLimiting?.windowMs / 60000) + ' minutes' : '100 requests per 15 minutes',
    gdprCompliance: isProduction && config.compliance?.gdprEnabled ? 'Enabled' : 'Disabled',
    maintenanceMode: isProduction && config.features?.maintenanceMode ? 'ACTIVE' : 'Inactive'
  });
});

module.exports = app;