require('dotenv').config();

const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const path = require('path');
const logger = require('./config/logger');
const webhookController = require('./controllers/webhookControllerCompliant');
const thrivecartController = require('./controllers/thrivecartController');
const { validateWebhook } = require('./middleware/validation');
const { checkStopKeywords, checkStartKeyword, checkMessageWindow } = require('./middleware/compliance');
const { globalRateLimiter, perUserRateLimiter } = require('./middleware/rateLimiter');
const AuthMiddleware = require('./middleware/auth');

const app = express();

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  crossOriginEmbedderPolicy: true,
  crossOriginOpenerPolicy: true,
  crossOriginResourcePolicy: { policy: 'same-origin' },
  dnsPrefetchControl: true,
  frameguard: { action: 'deny' },
  hidePoweredBy: true,
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: false,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  xssFilter: true
}));

app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || false,
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-twilio-signature', 'x-thrivecart-signature']
}));

app.use((req, res, next) => {
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  next();
});

app.use(express.urlencoded({ extended: false }));
app.use(express.json());

app.use('/legal', express.static(path.join(__dirname, '../public/legal')));

app.get('/privacy', (req, res) => {
  res.redirect('/legal/privacy-policy.html');
});

app.get('/terms', (req, res) => {
  res.redirect('/legal/terms-of-service.html');
});

app.get('/health', async (req, res) => {
  try {
    const includeDb = req.query.db === 'true';
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };

    if (includeDb) {
      const { healthCheck } = require('./db/connection');
      const dbHealth = await healthCheck();
      health.database = dbHealth;
      if (dbHealth.status === 'unhealthy') {
        health.status = 'degraded';
      }
    }

    res.json(health);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

app.post('/webhook/whatsapp',
  globalRateLimiter,
  perUserRateLimiter,
  // IMPORTANT: Re-enable these for production! Disabled for ngrok testing only
  // validateWebhook,  // Validates request structure
  // webhookController.verifyWebhook,  // Validates Twilio signature
  checkStopKeywords,
  checkStartKeyword,
  checkMessageWindow,
  webhookController.handleIncomingMessage.bind(webhookController)
);

app.head('/webhook/thrivecart', (req, res) => {
  res.status(200).send();
});

app.post('/webhook/thrivecart',
  express.urlencoded({ extended: true, limit: '10mb' }),
  thrivecartController.handleWebhook.bind(thrivecartController)
);

app.post('/admin/login', AuthMiddleware.handleAdminLogin);

if (process.env.NODE_ENV === 'development' || process.env.ADMIN_ACCESS === 'true') {
  app.use('/admin/*', AuthMiddleware.authenticateAdmin);

  app.get('/admin/compliance/stats', async (req, res) => {
    try {
      const enhancedRetentionService = require('./services/enhancedRetentionService');
      const complianceAuditService = require('./services/complianceAuditService');
      const auditStats = await complianceAuditService.getAuditStats();
      const retentionStats = await enhancedRetentionService.getRetentionStats();
      const minimizationRecs = await enhancedRetentionService.getDataMinimizationRecommendations();
      res.json({
        audit: auditStats,
        retention: retentionStats,
        recommendations: minimizationRecs,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  app.get('/admin/compliance/report', async (req, res) => {
    try {
      const complianceAuditService = require('./services/complianceAuditService');
      const { start, end } = req.query;
      const startDate = start || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
      const endDate = end || new Date().toISOString();
      const report = await complianceAuditService.generateComplianceReport(startDate, endDate);
      res.json(report);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  app.post('/admin/compliance/cleanup', async (req, res) => {
    try {
      const enhancedRetentionService = require('./services/enhancedRetentionService');
      const { type = 'regular' } = req.body;
      const result = await enhancedRetentionService.manualCleanup(type);
      res.json({ success: true, result });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  app.get('/admin/compliance/check', async (req, res) => {
    try {
      const enhancedRetentionService = require('./services/enhancedRetentionService');
      const compliance = await enhancedRetentionService.checkRetentionCompliance();
      res.json(compliance);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
}

if (process.env.PAYMENT_TEST_MODE === 'true') {
  const testRouter = require('./routes/testRouter');
  app.use('/test', testRouter);
  logger.info('Test endpoints enabled - mounted at /test');
}

class SafeErrorHandler {
  static handle(err, req, res, next) {
    const requestId = require('crypto').randomBytes(8).toString('hex');
    logger.error('Application error', {
      error: err.message,
      stack: err.stack,
      url: req.url,
      method: req.method,
      ip: req.ip,
      requestId
    });
    const isDev = process.env.NODE_ENV === 'development';
    const statusCode = err.status || 500;
    const errorResponse = {
      error: isDev ? err.message : 'An error occurred',
      code: err.code || 'INTERNAL_ERROR',
      requestId
    };
    if (isDev && err.stack) {
      errorResponse.stack = err.stack.split('\n');
    }
    res.status(statusCode).json(errorResponse);
  }
}

app.use(SafeErrorHandler.handle);

app.use((req, res) => {
  res.status(404).json({ error: 'Not found' });
});

module.exports = app;


