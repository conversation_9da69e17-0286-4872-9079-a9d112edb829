/**
 * Optimized Database Connection Pool Configuration
 * Provides enhanced connection pooling with monitoring and health checks
 */

const { Pool } = require('pg');
const logger = require('../config/logger');

// Pool configuration based on environment
const getPoolConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  const config = {
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    
    // Connection Pool Settings
    max: parseInt(process.env.DB_POOL_MAX) || (isProduction ? 30 : 10), // More connections in production
    min: parseInt(process.env.DB_POOL_MIN) || 2, // Maintain minimum connections
    
    // Timeout Settings
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000, // 30 seconds
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 3000, // 3 seconds
    
    // Query Timeout
    statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT) || 30000, // 30 second query timeout
    query_timeout: parseInt(process.env.DB_QUERY_TIMEOUT) || 30000, // 30 second query timeout
    
    // Keep Alive
    keepAlive: true,
    keepAliveInitialDelayMillis: parseInt(process.env.DB_KEEPALIVE_DELAY) || 10000, // 10 seconds
    
    // Application Name (helps with debugging)
    application_name: 'lock-in-habit-tracker',
  };
  
  return config;
};

// Create main pool for read/write operations
const pool = new Pool(getPoolConfig());

// Track pool statistics
let poolStats = {
  totalConnections: 0,
  idleConnections: 0,
  waitingRequests: 0,
  errors: 0,
  lastError: null,
  lastCheck: null
};

// Update pool statistics
const updatePoolStats = () => {
  poolStats = {
    totalConnections: pool.totalCount,
    idleConnections: pool.idleCount,
    waitingRequests: pool.waitingCount,
    errors: poolStats.errors,
    lastError: poolStats.lastError,
    lastCheck: new Date().toISOString()
  };
};

// Pool event handlers
pool.on('connect', (client) => {
  logger.debug('New database client connected to pool');
  updatePoolStats();
});

pool.on('acquire', (client) => {
  logger.debug('Database client acquired from pool');
  updatePoolStats();
});

pool.on('error', (err, client) => {
  logger.error('Unexpected error on idle database client', { 
    error: err.message,
    stack: err.stack 
  });
  poolStats.errors++;
  poolStats.lastError = err.message;
  updatePoolStats();
});

pool.on('remove', (client) => {
  logger.debug('Database client removed from pool');
  updatePoolStats();
});

// Health check function
const healthCheck = async () => {
  let client;
  try {
    client = await pool.connect();
    const result = await client.query('SELECT NOW() as current_time, version() as pg_version');
    updatePoolStats();
    
    return {
      status: 'healthy',
      timestamp: result.rows[0].current_time,
      version: result.rows[0].pg_version,
      pool: {
        ...poolStats,
        config: {
          max: pool.options.max,
          min: pool.options.min || 0,
          idleTimeout: pool.options.idleTimeoutMillis,
          connectionTimeout: pool.options.connectionTimeoutMillis
        }
      }
    };
  } catch (error) {
    logger.error('Database health check failed', { error: error.message });
    poolStats.errors++;
    poolStats.lastError = error.message;
    
    return {
      status: 'unhealthy',
      error: error.message,
      pool: poolStats
    };
  } finally {
    if (client) {
      client.release();
    }
  }
};

// Connection retry logic
const executeWithRetry = async (query, params, maxRetries = 3) => {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await pool.query(query, params);
      return result;
    } catch (error) {
      lastError = error;
      logger.warn(`Database query attempt ${attempt}/${maxRetries} failed`, {
        error: error.message,
        query: query.substring(0, 100)
      });
      
      // Only retry on connection errors, not on query errors
      if (error.code !== 'ECONNREFUSED' && error.code !== 'ETIMEDOUT' && error.code !== '57P03') {
        throw error;
      }
      
      // Wait before retrying (exponential backoff)
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt - 1)));
      }
    }
  }
  
  throw lastError;
};

// Graceful shutdown
const shutdown = async () => {
  logger.info('Shutting down database connection pool');
  try {
    await pool.end();
    logger.info('Database pool closed successfully');
  } catch (error) {
    logger.error('Error closing database pool', { error: error.message });
  }
};

// Test connection on startup
const testConnection = async () => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT 1 as test');
    client.release();
    
    logger.info('Database connection pool established successfully', {
      poolSize: pool.options.max,
      minConnections: pool.options.min || 0,
      idleTimeout: pool.options.idleTimeoutMillis,
      connectionTimeout: pool.options.connectionTimeoutMillis
    });
    
    return true;
  } catch (error) {
    logger.error('Failed to establish database connection', { error: error.message });
    throw error;
  }
};

// Initialize connection test
testConnection().catch(err => {
  logger.error('Database initialization failed', { error: err.message });
});

// Schedule periodic health checks (every 5 minutes) - disable in tests
if (process.env.NODE_ENV !== 'test' && process.env.DB_DISABLE_TIMERS !== 'true') {
  setInterval(async () => {
    const health = await healthCheck();
    if (health.status === 'unhealthy') {
      logger.error('Database health check failed', health);
    } else {
      logger.debug('Database health check passed', health);
    }
  }, 5 * 60 * 1000);
}

// Handle process termination
process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);

module.exports = {
  pool,
  healthCheck,
  executeWithRetry,
  shutdown,
  getPoolStats: () => ({ ...poolStats })
};