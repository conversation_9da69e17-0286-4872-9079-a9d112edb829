const Joi = require('joi');
const validator = require('validator');
const DOMPurify = require('isomorphic-dompurify');
const logger = require('../config/logger');

// E.164 phone format validation (supports WhatsApp prefix)
const phoneSchema = Joi.string()
  .pattern(/^(whatsapp:)?\+[1-9]\d{6,14}$/)
  .required();

// WhatsApp webhook validation schema with enhanced rules
const webhookSchema = Joi.object({
  Body: Joi.string().max(4096).required(),
  From: phoneSchema,
  To: phoneSchema,
  MessageSid: Joi.string().alphanum().required(),
  AccountSid: Joi.string().alphanum().required(),
  NumMedia: Joi.string().optional(),
  SmsMessageSid: Joi.string().optional(),
  SmsStatus: Joi.string().optional(),
  MessageStatus: Joi.string().optional(),
  ProfileName: Joi.string().max(256).optional(),
  WaId: Joi.string().pattern(/^[0-9]+$/).optional()
}).unknown(true); // Allow additional fields

// Comprehensive input sanitization
function sanitizeInput(text) {
  if (!text) return '';
  
  // Trim whitespace
  let sanitized = text.trim();
  
  // XSS protection - remove all HTML tags and scripts
  sanitized = DOMPurify.sanitize(sanitized, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    RETURN_TRUSTED_TYPE: false
  });
  
  // Additional XSS protection - escape HTML entities
  sanitized = validator.escape(sanitized);
  
  // SQL injection prevention - check for common patterns
  const sqlPatterns = [
    /(\bDROP\s+TABLE\b)/gi,
    /(\bDELETE\s+FROM\b)/gi,
    /(\bINSERT\s+INTO\b)/gi,
    /(\bUPDATE\s+.+\s+SET\b)/gi,
    /(\bSELECT\s+.+\s+FROM\b)/gi,
    /(\bALTER\s+TABLE\b)/gi,
    /(\bCREATE\s+TABLE\b)/gi,
    /(\bEXEC\s*\()/gi,
    /(\bUNION\s+SELECT\b)/gi,
    /(;\s*DROP\b)/gi,
    /(--\s*$)/gm, // SQL comments
    /(\/\*[\s\S]*?\*\/)/g, // Block comments
    /(\bOR\s+1\s*=\s*1\b)/gi,
    /(\bAND\s+1\s*=\s*1\b)/gi
  ];
  
  for (const pattern of sqlPatterns) {
    if (pattern.test(sanitized)) {
      logger.warn('SQL injection attempt detected and blocked', {
        attempt: sanitized.substring(0, 100),
        pattern: pattern.toString()
      });
      // Remove the malicious content
      sanitized = sanitized.replace(pattern, '').trim();
    }
  }
  
  // Check for script injection attempts
  if (validator.contains(sanitized.toLowerCase(), '<script') ||
      validator.contains(sanitized.toLowerCase(), 'javascript:') ||
      validator.contains(sanitized.toLowerCase(), 'onerror=') ||
      validator.contains(sanitized.toLowerCase(), 'onload=')) {
    logger.warn('Script injection attempt detected', { 
      attempt: sanitized.substring(0, 50) 
    });
    // Remove suspicious content
    sanitized = sanitized
      .replace(/<script[^>]*>.*?<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }
  
  // Normalize whitespace
  sanitized = sanitized.replace(/\s+/g, ' ');
  
  // Length limit for WhatsApp messages
  return sanitized.substring(0, 1000);
}

// Validate and sanitize email
function sanitizeEmail(email) {
  if (!email) return '';
  
  const trimmed = email.trim().toLowerCase();
  
  if (!validator.isEmail(trimmed)) {
    logger.warn('Invalid email format', { email: trimmed.substring(0, 50) });
    return '';
  }
  
  // Normalize email
  return validator.normalizeEmail(trimmed, {
    gmail_remove_dots: false,
    gmail_remove_subaddress: false,
    outlookdotcom_remove_subaddress: false,
    yahoo_remove_subaddress: false,
    icloud_remove_subaddress: false
  }) || '';
}

// Validate and sanitize URL
function sanitizeURL(url) {
  if (!url) return '';
  
  const trimmed = url.trim();
  
  if (!validator.isURL(trimmed, {
    protocols: ['http', 'https'],
    require_protocol: true,
    require_valid_protocol: true,
    allow_query_components: true,
    allow_fragments: true
  })) {
    logger.warn('Invalid URL format', { url: trimmed.substring(0, 100) });
    return '';
  }
  
  return trimmed;
}

// Sanitize numeric input
function sanitizeNumber(value, options = {}) {
  const {
    min = 0,
    max = Number.MAX_SAFE_INTEGER,
    allowFloat = false
  } = options;
  
  if (value === null || value === undefined) return null;
  
  const str = String(value).trim();
  
  if (allowFloat ? !validator.isFloat(str) : !validator.isInt(str)) {
    return null;
  }
  
  const num = allowFloat ? parseFloat(str) : parseInt(str, 10);
  
  if (isNaN(num)) return null;
  
  // Clamp to range
  return Math.min(Math.max(num, min), max);
}

// Middleware to validate webhook requests
const validateWebhook = (req, res, next) => {
  const { error, value } = webhookSchema.validate(req.body);

  if (error) {
    logger.warn('Invalid webhook request', {
      error: error.details[0].message,
      ip: req.ip,
      path: req.path
    });
    return res.status(400).send('Invalid request');
  }

  // Sanitize all string fields
  req.body = value;
  if (req.body.Body) {
    req.body.Body = sanitizeInput(req.body.Body);
  }

  if (req.body.ProfileName) {
    req.body.ProfileName = sanitizeInput(req.body.ProfileName);
  }

  next();
};

// Validate phone number format
const validatePhone = (phone) => {
  if (!phone) return false;

  const { error } = phoneSchema.validate(phone);
  if (error) return false;

  // Additional validation - more lenient for testing
  const cleanPhone = phone.replace('whatsapp:', '');

  // Basic E.164 format check
  if (!/^\+[1-9]\d{6,14}$/.test(cleanPhone)) {
    return false;
  }

  return true;
};

// Validate payment webhook data
const validatePaymentData = (data) => {
  const schema = Joi.object({
    event: Joi.string().required(),
    customer_email: Joi.string().email().required(),
    customer_name: Joi.string().max(256).optional(),
    order_id: Joi.string().max(100).required(),
    order_total: Joi.number().positive().optional(),
    currency: Joi.string().length(3).optional(),
    product_name: Joi.string().max(256).optional()
  }).unknown(true);
  
  const { error, value } = schema.validate(data);
  
  if (error) {
    logger.warn('Invalid payment data', { 
      error: error.details[0].message 
    });
    return null;
  }
  
  // Sanitize string fields
  if (value.customer_email) {
    value.customer_email = sanitizeEmail(value.customer_email);
  }
  
  if (value.customer_name) {
    value.customer_name = sanitizeInput(value.customer_name);
  }
  
  if (value.product_name) {
    value.product_name = sanitizeInput(value.product_name);
  }
  
  return value;
};

// Create a safe SQL identifier (for table/column names)
function safeSQLIdentifier(identifier) {
  if (!identifier) return '';
  
  // Only allow alphanumeric and underscore
  const safe = identifier.replace(/[^a-zA-Z0-9_]/g, '');
  
  // Must start with letter or underscore
  if (!/^[a-zA-Z_]/.test(safe)) {
    return '';
  }
  
  // Limit length
  return safe.substring(0, 63); // PostgreSQL identifier limit
}

module.exports = {
  validateWebhook,
  validatePhone,
  validatePaymentData,
  sanitizeInput,
  sanitizeEmail,
  sanitizeURL,
  sanitizeNumber,
  safeSQLIdentifier
};