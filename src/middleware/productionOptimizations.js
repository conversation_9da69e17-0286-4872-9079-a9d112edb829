const compression = require('compression');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const logger = require('../config/logger');

/**
 * Production Optimizations Middleware
 * Applies performance and security enhancements for production
 */
class ProductionOptimizations {
  /**
   * Apply all production optimizations
   */
  static applyAll(app) {
    if (process.env.NODE_ENV !== 'production') {
      logger.info('Skipping production optimizations in non-production environment');
      return;
    }

    // Apply compression
    this.applyCompression(app);
    
    // Apply security headers
    this.applySecurityHeaders(app);
    
    // Apply rate limiting
    this.applyRateLimiting(app);
    
    // Apply response optimizations
    this.applyResponseOptimizations(app);
    
    // Apply monitoring
    this.applyMonitoring(app);
    
    logger.info('Production optimizations applied successfully');
  }

  /**
   * Apply compression middleware
   */
  static applyCompression(app) {
    app.use(compression({
      level: 6, // Balanced compression level
      threshold: 1024, // Only compress responses larger than 1KB
      filter: (req, res) => {
        // Don't compress responses with no-transform directive
        if (res.getHeader('Cache-Control') && 
            res.getHeader('Cache-Control').indexOf('no-transform') > -1) {
          return false;
        }
        // Use default compression filter
        return compression.filter(req, res);
      }
    }));
    
    logger.info('Compression middleware enabled');
  }

  /**
   * Apply enhanced security headers
   */
  static applySecurityHeaders(app) {
    app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    }));
    
    // Additional security headers
    app.use((req, res, next) => {
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');
      res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
      res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
      next();
    });
    
    logger.info('Security headers configured');
  }

  /**
   * Apply rate limiting
   */
  static applyRateLimiting(app) {
    // API rate limiter
    const apiLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // Limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        logger.warn('Rate limit exceeded', {
          ip: req.ip,
          path: req.path,
          method: req.method
        });
        res.status(429).json({
          error: 'Too many requests',
          retryAfter: Math.ceil(req.rateLimit.resetTime / 1000)
        });
      }
    });

    // Strict limiter for auth endpoints
    const authLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // Limit each IP to 5 requests per windowMs
      skipSuccessfulRequests: true,
      message: 'Too many authentication attempts, please try again later.'
    });

    // Apply limiters
    app.use('/api/', apiLimiter);
    app.use('/admin/login', authLimiter);
    app.use('/webhook/', apiLimiter);
    
    logger.info('Rate limiting configured');
  }

  /**
   * Apply response optimizations
   */
  static applyResponseOptimizations(app) {
    // Remove unnecessary headers
    app.use((req, res, next) => {
      res.removeHeader('X-Powered-By');
      next();
    });

    // Add cache headers for static content
    app.use((req, res, next) => {
      if (req.method === 'GET') {
        const path = req.path.toLowerCase();
        
        // Cache static assets
        if (path.match(/\.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
          res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
        }
        // Cache HTML for a short time
        else if (path.match(/\.html$/) || path === '/') {
          res.setHeader('Cache-Control', 'public, max-age=300, must-revalidate');
        }
        // Don't cache API responses by default
        else if (path.startsWith('/api/') || path.startsWith('/webhook/')) {
          res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
        }
      }
      next();
    });
    
    logger.info('Response optimizations applied');
  }

  /**
   * Apply monitoring and metrics
   */
  static applyMonitoring(app) {
    // Request timing
    app.use((req, res, next) => {
      req.startTime = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - req.startTime;
        
        // Log slow requests
        if (duration > 1000) {
          logger.warn('Slow request detected', {
            method: req.method,
            path: req.path,
            duration: `${duration}ms`,
            statusCode: res.statusCode
          });
        }
        
        // Log request metrics
        if (process.env.METRICS_ENABLED === 'true') {
          logger.debug('Request completed', {
            method: req.method,
            path: req.path,
            duration: `${duration}ms`,
            statusCode: res.statusCode,
            ip: req.ip
          });
        }
      });
      
      next();
    });
    
    // Memory usage monitoring
    if (process.env.ENABLE_PERFORMANCE_MONITORING === 'true') {
      setInterval(() => {
        const memUsage = process.memoryUsage();
        const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
        const heapTotalMB = Math.round(memUsage.heapTotal / 1024 / 1024);
        const rssMB = Math.round(memUsage.rss / 1024 / 1024);
        
        if (heapUsedMB > 500) {
          logger.warn('High memory usage detected', {
            heapUsed: `${heapUsedMB}MB`,
            heapTotal: `${heapTotalMB}MB`,
            rss: `${rssMB}MB`
          });
        }
      }, 60000); // Check every minute
    }
    
    logger.info('Monitoring configured');
  }
}

module.exports = ProductionOptimizations;