/**
 * DatabaseSecurity - Utilities for secure database operations
 * Prevents SQL injection and validates queries
 */

const logger = require('../config/logger');

class DatabaseSecurity {
  /**
   * Whitelist of allowed table names
   */
  static ALLOWED_TABLES = [
    'users',
    'habits',
    'habit_logs',
    'audit_logs',
    'access_codes',
    'paid_users',
    'user_consents',
    'data_requests',
    'data_exports',
    'user_sessions',
    'email_queue',
    'webhook_logs',
    'subscription_tracking',
    'payment_logs'
  ];

  /**
   * Whitelist of allowed column names
   */
  static ALLOWED_COLUMNS = [
    'id', 'user_id', 'phone', 'phone_number', 'display_name', 
    'status', 'timezone', 'current_state', 'session_context',
    'last_active', 'is_unlocked', 'access_code', 'paid_at',
    'marketing_consent', 'analytics_consent', 'opted_out_at',
    'created_at', 'updated_at', 'habit_name', 'habit_number',
    'log_date', 'completed', 'logged_at', 'event_type',
    'event_data', 'timestamp', 'code', 'used_by', 'used_at',
    'expires_at', 'email', 'amount', 'currency', 'subscription_id'
  ];

  /**
   * Validate and sanitize table name
   * @param {string} tableName - Table name to validate
   * @returns {string} Validated table name
   * @throws {Error} If table name is not allowed
   */
  static validateTableName(tableName) {
    if (!tableName || typeof tableName !== 'string') {
      throw new Error('Invalid table name: must be a non-empty string');
    }

    const normalizedTable = tableName.toLowerCase().trim();
    
    if (!this.ALLOWED_TABLES.includes(normalizedTable)) {
      logger.error('SQL injection attempt detected', {
        attemptedTable: tableName,
        normalized: normalizedTable
      });
      throw new Error(`Invalid table name: ${tableName}`);
    }

    return normalizedTable;
  }

  /**
   * Validate and sanitize column name
   * @param {string} columnName - Column name to validate
   * @returns {string} Validated column name
   * @throws {Error} If column name is not allowed
   */
  static validateColumnName(columnName) {
    if (!columnName || typeof columnName !== 'string') {
      throw new Error('Invalid column name: must be a non-empty string');
    }

    const normalizedColumn = columnName.toLowerCase().trim();
    
    if (!this.ALLOWED_COLUMNS.includes(normalizedColumn)) {
      logger.error('SQL injection attempt detected', {
        attemptedColumn: columnName,
        normalized: normalizedColumn
      });
      throw new Error(`Invalid column name: ${columnName}`);
    }

    return normalizedColumn;
  }

  /**
   * Build a safe DELETE query with validated table name
   * @param {string} tableName - Table name
   * @param {string} whereColumn - Column for WHERE clause
   * @returns {string} Safe SQL query string
   */
  static buildDeleteQuery(tableName, whereColumn = 'user_id') {
    const safeTable = this.validateTableName(tableName);
    const safeColumn = this.validateColumnName(whereColumn);
    
    return `DELETE FROM ${safeTable} WHERE ${safeColumn} = $1`;
  }

  /**
   * Build a safe SELECT query with validated table and columns
   * @param {string} tableName - Table name
   * @param {Array<string>} columns - Column names to select
   * @param {string} whereColumn - Column for WHERE clause
   * @returns {string} Safe SQL query string
   */
  static buildSelectQuery(tableName, columns = ['*'], whereColumn = null) {
    const safeTable = this.validateTableName(tableName);
    
    let columnList = '*';
    if (columns[0] !== '*') {
      columnList = columns.map(col => this.validateColumnName(col)).join(', ');
    }
    
    let query = `SELECT ${columnList} FROM ${safeTable}`;
    
    if (whereColumn) {
      const safeWhereColumn = this.validateColumnName(whereColumn);
      query += ` WHERE ${safeWhereColumn} = $1`;
    }
    
    return query;
  }

  /**
   * Build a safe UPDATE query with validated table and columns
   * @param {string} tableName - Table name
   * @param {Array<string>} updateColumns - Columns to update
   * @param {string} whereColumn - Column for WHERE clause
   * @returns {string} Safe SQL query string
   */
  static buildUpdateQuery(tableName, updateColumns, whereColumn = 'id') {
    const safeTable = this.validateTableName(tableName);
    const safeWhereColumn = this.validateColumnName(whereColumn);
    
    const setClauses = updateColumns.map((col, index) => {
      const safeCol = this.validateColumnName(col);
      return `${safeCol} = $${index + 2}`;
    }).join(', ');
    
    return `UPDATE ${safeTable} SET ${setClauses} WHERE ${safeWhereColumn} = $1`;
  }

  /**
   * Build a safe INSERT query with validated table and columns
   * @param {string} tableName - Table name
   * @param {Array<string>} columns - Column names to insert
   * @returns {string} Safe SQL query string
   */
  static buildInsertQuery(tableName, columns) {
    const safeTable = this.validateTableName(tableName);
    const safeColumns = columns.map(col => this.validateColumnName(col));
    
    const columnList = safeColumns.join(', ');
    const valuePlaceholders = safeColumns.map((_, index) => `$${index + 1}`).join(', ');
    
    return `INSERT INTO ${safeTable} (${columnList}) VALUES (${valuePlaceholders})`;
  }

  /**
   * Validate query parameters to ensure they're safe
   * @param {Array} params - Query parameters
   * @returns {Array} Validated parameters
   * @throws {Error} If parameters contain SQL injection attempts
   */
  static validateParameters(params) {
    if (!Array.isArray(params)) {
      throw new Error('Parameters must be an array');
    }

    // Check for SQL injection patterns in string parameters
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|ALTER|CREATE|EXEC|EXECUTE)\b)/i,
      /(--|\/\*|\*\/|;|\\x00|\\n|\\r|\\t)/,
      /(\bOR\b.*\b=\b|\bAND\b.*\b=\b)/i
    ];

    for (const param of params) {
      if (typeof param === 'string') {
        for (const pattern of sqlPatterns) {
          if (pattern.test(param)) {
            logger.error('SQL injection attempt in parameter', {
              param: param.substring(0, 50),
              pattern: pattern.toString()
            });
            throw new Error('Invalid parameter: potential SQL injection');
          }
        }
      }
    }

    return params;
  }

  /**
   * Escape identifier for use in dynamic SQL (as a last resort)
   * @param {string} identifier - Identifier to escape
   * @returns {string} Escaped identifier
   */
  static escapeIdentifier(identifier) {
    if (!identifier || typeof identifier !== 'string') {
      throw new Error('Invalid identifier');
    }

    // Remove any quotes and escape them
    return '"' + identifier.replace(/"/g, '""') + '"';
  }

  /**
   * Check if a query is safe to execute
   * @param {string} query - SQL query to check
   * @returns {boolean} Whether query appears safe
   */
  static isQuerySafe(query) {
    const dangerousPatterns = [
      /DROP\s+(TABLE|DATABASE|SCHEMA|VIEW|INDEX)/i,
      /TRUNCATE\s+TABLE/i,
      /ALTER\s+TABLE.*DROP/i,
      /DELETE\s+FROM.*WHERE\s+1\s*=\s*1/i,
      /UPDATE.*SET.*WHERE\s+1\s*=\s*1/i,
      /;\s*(DROP|DELETE|UPDATE|INSERT|ALTER|CREATE)/i
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(query)) {
        logger.error('Dangerous query pattern detected', {
          query: query.substring(0, 100),
          pattern: pattern.toString()
        });
        return false;
      }
    }

    return true;
  }

  /**
   * Execute a safe query using the database pool
   * @param {string} query - SQL query to execute
   * @param {Array} params - Query parameters
   * @returns {Promise} Query result
   */
  static async query(query, params = []) {
    // Validate the query is safe
    if (!this.isQuerySafe(query)) {
      throw new Error('Query contains dangerous patterns');
    }

    // Validate parameters
    const safeParams = this.validateParameters(params);

    // Get the database pool
    const pool = require('../db/connection');
    
    try {
      const result = await pool.query(query, safeParams);
      return result;
    } catch (error) {
      logger.error('Database query error', {
        error: error.message,
        query: query.substring(0, 100)
      });
      throw error;
    }
  }
}

module.exports = DatabaseSecurity;