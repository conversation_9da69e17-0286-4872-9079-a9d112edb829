# Architecture Documentation

**Lock In - WhatsApp Habit Tracker Bot**
**Architecture Style**: Layered Monolithic with Strategy Pattern
**Version**: 2.9.0
**Last Updated**: September 7, 2025

---

## Table of Contents

- [System Overview](#system-overview)
- [Architecture Patterns](#architecture-patterns)
- [Component Architecture](#component-architecture)
- [Data Flow](#data-flow)
- [Technology Stack](#technology-stack)
- [Security Architecture](#security-architecture)
- [Scalability Considerations](#scalability-considerations)

---

## System Overview

Lock In is a production-ready WhatsApp habit tracking bot built with a layered monolithic architecture. The system processes WhatsApp messages through a state machine, manages user subscriptions via payment webhooks, and provides comprehensive habit tracking analytics.

### High-Level Architecture

```mermaid
graph TB
    subgraph "External Services"
        WA[WhatsApp Users]
        TW[Twilio API]
        TC[ThriveCart]
        SMTP[Email Provider]
    end
    
    subgraph "Application Layer"
        LB[Load Balancer]
        APP[Express Server]
        MW[Middleware Stack]
    end
    
    subgraph "Business Logic Layer"
        SM[State Machine]
        PS[Payment Service]
        ES[Email Service]
        CS[Compliance Service]
        SS[Session Service]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL)]
        CACHE[(Redis - Future)]
    end
    
    WA --> TW
    TW --> LB
    TC --> LB
    LB --> APP
    APP --> MW
    MW --> SM
    MW --> PS
    MW --> ES
    MW --> CS
    SM --> SS
    PS --> PG
    ES --> SMTP
    CS --> PG
    SS --> PG
```

### Core Principles

1. **Separation of Concerns**: Clear boundaries between presentation, business logic, and data layers
2. **Strategy Pattern**: Unified state machine with pluggable strategies for different concerns
3. **GDPR by Design**: Privacy-first architecture with built-in compliance features
4. **Webhook-Driven**: Event-driven architecture for payment and message processing
5. **Stateful Conversations**: Session-based user interactions with timeout management

---

## Architecture Patterns

### Primary Patterns

#### 1. Layered Architecture
```
┌─────────────────────────────────────┐
│         Presentation Layer          │ ← Express Routes, Middleware
├─────────────────────────────────────┤
│         Business Logic Layer        │ ← Services, State Machine
├─────────────────────────────────────┤
│         Data Access Layer           │ ← Models, Database Queries
├─────────────────────────────────────┤
│         Infrastructure Layer        │ ← Database, External APIs
└─────────────────────────────────────┘
```

#### 2. Strategy Pattern (State Machine)
```javascript
// Unified State Machine with Strategies
class UnifiedStateMachine {
  constructor() {
    this.strategies = [
      new CoreStateMachineStrategy(),
      new PaymentEnforcementStrategy(),
      new ComplianceStrategy()
    ];
  }
  
  async processMessage(user, message) {
    let context = { user, message, response: null };
    
    for (const strategy of this.strategies) {
      context = await strategy.process(context);
      if (context.shouldStop) break;
    }
    
    return context.response;
  }
}
```

#### 3. Repository Pattern
```javascript
// Data Access Abstraction
class UserRepository {
  static async findOrCreate(phone) {
    // Database operations abstracted
  }
  
  static async updateState(userId, newState) {
    // State management abstracted
  }
}
```

### Secondary Patterns

- **Singleton**: Services are instantiated once and reused
- **Template Method**: Email templates with customizable content
- **Observer**: Audit logging for compliance events
- **Factory**: Access code generation with different formats

---

## Component Architecture

### 1. Presentation Layer

#### Express Server (`src/server.js`)
- **Responsibility**: HTTP request handling, middleware orchestration
- **Key Features**: Security headers, rate limiting, webhook verification
- **Endpoints**: 15 total (public, webhook, admin, test)

#### Middleware Stack (`src/middleware/`)
```javascript
// Middleware Pipeline
app.post('/webhook/whatsapp',
  globalRateLimiter,           // Global rate limiting
  perUserRateLimiter,          // Per-user rate limiting  
  validateWebhook,             // Input validation
  verifyWebhook,               // Twilio signature verification
  checkStopKeywords,           // STOP keyword compliance
  checkStartKeyword,           // START keyword handling
  checkMessageWindow,          // 24-hour window check
  handleIncomingMessage        // Main message handler
);
```

### 2. Business Logic Layer

#### Unified State Machine (`src/services/unifiedStateMachine.js`)
```javascript
// Strategy-based State Machine
const strategies = [
  CoreStateMachineStrategy,      // Base conversation logic
  PaymentEnforcementStrategy,    // Payment gating
  ComplianceStrategy            // GDPR/WhatsApp compliance
];
```

**State Flow:**
```
LOCKED → ONBOARDING → ACTIVE
   ↓         ↓          ↓
 [Access]  [Setup]   [Tracking]
```

#### Core Services
- **Payment Service**: Subscription management and webhook processing
- **Email Service**: Template-based email automation with queue processing
- **Session Manager**: 30-minute timeout with automatic cleanup
- **Compliance Service**: GDPR compliance and audit logging

### 3. Data Access Layer

#### Models (`src/models/`)
- **User Model**: User management with state tracking
- **Habit Model**: Habit CRUD operations and progress calculation
- **AuditLog Model**: Compliance event logging

#### Database Schema
```sql
-- Core Tables
users (17 fields)
├── Core: id, phone, display_name, status, timezone
├── Session: current_state, session_context, last_active
├── Payment: is_unlocked, access_code, paid_at
├── Compliance: marketing_consent, analytics_consent
└── Timestamps: created_at, updated_at

habits (6 fields)
├── Core: id, user_id, habit_number, habit_name
└── Metadata: created_at, updated_at

habit_logs (6 fields)
├── Core: id, user_id, habit_id, log_date, completed
└── Timestamp: logged_at
```

---

## Data Flow

### 1. WhatsApp Message Processing

```mermaid
sequenceDiagram
    participant U as User
    participant T as Twilio
    participant A as App
    participant S as State Machine
    participant D as Database
    
    U->>T: Send WhatsApp Message
    T->>A: POST /webhook/whatsapp
    A->>A: Validate & Rate Limit
    A->>D: Find/Create User
    A->>S: Process Message
    S->>D: Update User State
    S->>A: Return Response
    A->>T: TwiML Response
    T->>U: WhatsApp Reply
```

### 2. Payment Webhook Processing

```mermaid
sequenceDiagram
    participant P as Payment Provider
    participant A as App
    participant E as Email Service
    participant D as Database
    
    P->>A: Payment Webhook
    A->>A: Verify Signature
    A->>D: Create Paid User
    A->>D: Generate Access Code
    A->>E: Queue Welcome Email
    E->>E: Process Email Queue
    A->>P: 200 OK Response
```

### 3. Habit Tracking Flow

```mermaid
stateDiagram-v2
    [*] --> LOCKED
    LOCKED --> ONBOARDING: Valid Access Code
    ONBOARDING --> AWAITING_NAME: Start Setup
    AWAITING_NAME --> AWAITING_TIMEZONE: Name Provided
    AWAITING_TIMEZONE --> SETTING_HABIT: Timezone Set
    SETTING_HABIT --> ACTIVE: Habits Configured
    ACTIVE --> LOGGING_HABITS: Daily Check-in
    LOGGING_HABITS --> VIEWING_PROGRESS: View Stats
    VIEWING_PROGRESS --> ACTIVE: Continue Tracking
```

---

## Technology Stack

### Runtime & Framework
| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| **Runtime** | Node.js | 18-alpine | JavaScript execution environment |
| **Framework** | Express.js | 4.18.2 | Web application framework |
| **Database** | PostgreSQL | 15+ | Primary data storage |
| **Container** | Docker | Latest | Application containerization |

### Core Dependencies
| Library | Version | Purpose |
|---------|---------|---------|
| `express` | 4.18.2 | Web application framework |
| `twilio` | 4.19.0 | WhatsApp Business API integration |
| `pg` | 8.11.3 | PostgreSQL database driver with connection pooling |
| `joi` | 17.11.0 | Input validation and sanitization |
| `winston` | 3.11.0 | Structured logging with JSON format |
| `helmet` | 7.1.0 | Security headers and CSP |
| `express-rate-limit` | 7.1.5 | Rate limiting with progressive penalties |
| `jsonwebtoken` | 9.0.2 | JWT authentication for admin endpoints |
| `nodemailer` | 7.0.5 | Email sending via SMTP |
| `moment-timezone` | 0.5.43 | Timezone handling for users |
| `validator` | 13.15.15 | Additional input validation |
| `isomorphic-dompurify` | 2.26.0 | XSS prevention |

### External Integrations
- **Twilio**: WhatsApp Business API with signature verification
- **ThriveCart**: Payment processing with HMAC-SHA256 verification
- **Brevo SMTP**: Transactional email delivery
- **PostgreSQL**: Primary database with SSL support

---

## Security Architecture

### Defense in Depth

```mermaid
graph TB
    subgraph "Network Security"
        HTTPS[HTTPS/TLS]
        FW[Firewall Rules]
    end
    
    subgraph "Application Security"
        RL[Rate Limiting]
        VAL[Input Validation]
        HELM[Security Headers]
        JWT[JWT Authentication]
    end
    
    subgraph "Data Security"
        ENC[Data Encryption]
        PII[PII Redaction]
        AUDIT[Audit Logging]
    end
    
    subgraph "Infrastructure Security"
        DOCK[Non-root Docker]
        ENV[Environment Isolation]
        SEC[Secret Management]
    end
```

### Security Layers

1. **Network Security**
   - HTTPS enforcement for all endpoints
   - Webhook signature verification
   - IP-based rate limiting

2. **Application Security**
   - JWT authentication for admin endpoints
   - Input validation with Joi schemas
   - SQL injection prevention
   - XSS protection via Helmet

3. **Data Security**
   - PII redaction in logs
   - Encrypted data storage
   - GDPR-compliant data handling
   - Secure data deletion

4. **Compliance Security**
   - WhatsApp Business API compliance
   - STOP/START keyword handling
   - 24-hour message window enforcement
   - Comprehensive audit trails

---

## Scalability Considerations

### Current Limitations

1. **Monolithic Architecture**: Single point of failure
2. **In-Memory Sessions**: Cannot scale horizontally
3. **Single Database**: No read replicas
4. **Synchronous Processing**: Webhook processing blocks

### Scaling Strategies

#### Short-term (1-3 months)
```mermaid
graph TB
    subgraph "Current"
        APP1[App Instance]
        DB1[(PostgreSQL)]
    end
    
    subgraph "Short-term"
        LB[Load Balancer]
        APP2[App Instance 1]
        APP3[App Instance 2]
        REDIS[(Redis Sessions)]
        DB2[(PostgreSQL)]
    end
```

#### Medium-term (3-6 months)
```mermaid
graph TB
    subgraph "Microservices"
        API[API Gateway]
        CHAT[Chat Service]
        PAY[Payment Service]
        EMAIL[Email Service]
        COMP[Compliance Service]
    end
    
    subgraph "Data Layer"
        REDIS[(Redis)]
        MAIN[(Main DB)]
        READ[(Read Replica)]
        QUEUE[(Message Queue)]
    end
```

### Performance Optimization

1. **Caching Strategy**
   - Redis for session storage
   - Query result caching
   - User data caching

2. **Database Optimization**
   - Connection pooling
   - Read replicas
   - Query optimization

3. **Background Processing**
   - Email queue processing
   - Webhook queue processing
   - Cleanup job scheduling

---

## Future Architecture Evolution

### Phase 1: Caching Layer (Complete)
- ✅ Strategy pattern implementation
- ✅ SQL injection prevention
- ✅ JWT authentication

### Phase 2: Performance (In Progress)
- ✅ Redis session storage (Docker setup complete)
- ✅ Background job processing (Email queue implemented)
- ✅ Database optimization (Connection pooling, 20 connections)

### Phase 3: Microservices (Planned)
- [ ] Extract payment service
- [ ] Extract compliance service
- [ ] API gateway implementation

### Phase 4: Advanced Features (Future)
- [ ] Real-time analytics dashboard
- [ ] Machine learning insights for habit recommendations
- [ ] Multi-language support
- [ ] Mobile app integration

---

**Architecture Review**: Conducted September 7, 2025
**Next Review**: March 7, 2026
**Architecture Rating**: A (Excellent production-ready foundation with clear evolution path)
**Current Phase**: 2.9.0 - Production deployment with monitoring stack
