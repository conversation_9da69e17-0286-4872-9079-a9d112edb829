# Docker Setup Guide - Lock In Habit Tracker

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                     Docker Network: lockin_app-network       │
├─────────────────────────────────────────────────────────────┤
│                                                               │
│  ┌──────────────┐        ┌──────────────┐                   │
│  │              │        │              │                   │
│  │    Nginx     │◄──────►│   Node.js    │                   │
│  │   (Reverse   │        │     App      │                   │
│  │    Proxy)    │        │  Port 3000   │                   │
│  │              │        │              │                   │
│  └──────────────┘        └──────────────┘                   │
│         ▲                       │ │                          │
│         │                       │ │                          │
│    Ports 80/443                 │ │                          │
│    (External)                   ▼ ▼                          │
│                          ┌──────────────┐                   │
│                          │              │                   │
│                          │  PostgreSQL  │                   │
│                          │   Database   │                   │
│                          │  Port 5432   │                   │
│                          │              │                   │
│                          └──────────────┘                   │
│                                 ▲                            │
│                                 │                            │
│                          ┌──────────────┐                   │
│                          │              │                   │
│                          │    Redis     │                   │
│                          │    Cache     │                   │
│                          │  Port 6379   │                   │
│                          │              │                   │
│                          └──────────────┘                   │
│                                                               │
└─────────────────────────────────────────────────────────────┘
```

## 📦 What's Included

### Services
1. **PostgreSQL 15** - Primary database for user data, habits, and logs
2. **Redis 7** - Session storage and caching layer
3. **Node.js App** - Main application server (Express.js)
4. **Nginx** - Reverse proxy with SSL termination and load balancing

### Volumes (Persistent Data)
- `postgres-data` - Database files
- `redis-data` - Cache persistence
- `app-logs` - Application logs
- `nginx-cache` - Nginx cache storage

### Networks
- `lockin_app-network` - Internal Docker network for container communication

## 🚀 Quick Start

### 1. Prerequisites
```bash
# Check Docker is installed
docker --version  # Should be 20.10+
docker compose version  # Should be v2+

# Check ports are available
lsof -i :80     # Should be empty
lsof -i :443    # Should be empty
lsof -i :5432   # Should be empty (unless using external PostgreSQL)
lsof -i :6379   # Should be empty (unless using external Redis)
```

### 2. Environment Setup
```bash
# Copy production environment template
cp .env.production.example .env.production

# Edit with your actual values
nano .env.production

# Required variables:
DATABASE_URL=****************************************************/habittracker_prod
REDIS_URL=redis://redis:6379
TWILIO_AUTH_TOKEN=your_actual_token
TWILIO_ACCOUNT_SID=your_actual_sid
```

### 3. Generate SSL Certificates
```bash
# For development (self-signed)
cd docker/nginx/ssl
bash generate-self-signed.sh localhost
cd ../../..

# For production (use Let's Encrypt or real certificates)
# Place your cert.pem and key.pem in docker/nginx/ssl/
```

### 4. Start Everything
```bash
# Using docker-compose directly
docker compose -f docker-compose.production.yml up -d

# OR using Makefile (recommended)
make deploy

# Check status
docker ps
```

## 📁 Docker Files Explained

### `docker-compose.production.yml`
Main production configuration with all services:
- Full stack with Nginx, App, PostgreSQL, Redis
- Production-grade settings (resource limits, health checks)
- SSL/TLS enabled
- Volume persistence

### `docker-compose.yml`
Development configuration:
- Simplified setup for local development
- Hot-reload enabled
- Exposed database ports for debugging

### Docker Override Pattern
For local customizations:
- Copy `docker-compose.override.yml.example` to `docker-compose.override.yml`
- Customize ports, environment variables, and volumes
- The override file is git-ignored for local-only changes

### `docker-compose.monitoring.yml`
Monitoring stack (optional):
- Prometheus metrics collection
- Grafana dashboards
- Node/PostgreSQL/Redis exporters
- cAdvisor for container metrics

### `Dockerfile`
Production multi-stage build:
- Alpine Linux base (small size)
- Non-root user execution
- Health checks included
- Optimized layer caching

## 🔧 Common Operations

### View Logs
```bash
# All services
docker compose -f docker-compose.production.yml logs -f

# Specific service
docker compose -f docker-compose.production.yml logs -f app
docker compose -f docker-compose.production.yml logs -f postgres
docker compose -f docker-compose.production.yml logs -f nginx
docker compose -f docker-compose.production.yml logs -f redis

# Using Makefile
make logs
make logs-app
make logs-db
```

### Database Operations
```bash
# Connect to PostgreSQL
docker exec -it lockin-postgres psql -U habituser -d habittracker_prod

# Run migrations
docker exec lockin-app npm run migrate

# Backup database
docker exec lockin-postgres pg_dump -U habituser habittracker_prod > backup.sql

# Restore database
docker exec -i lockin-postgres psql -U habituser habittracker_prod < backup.sql
```

### Redis Operations
```bash
# Connect to Redis CLI
docker exec -it lockin-redis redis-cli

# Check Redis is working
docker exec -it lockin-redis redis-cli ping

# View all keys
docker exec -it lockin-redis redis-cli keys '*'
```

### Restart Services
```bash
# Restart everything
docker compose -f docker-compose.production.yml restart

# Restart specific service
docker restart lockin-app
docker restart lockin-nginx
```

### Stop Everything
```bash
# Stop and remove containers (data persists in volumes)
docker compose -f docker-compose.production.yml down

# Stop and remove everything including volumes (WARNING: Data loss!)
docker compose -f docker-compose.production.yml down -v
```

## 🏃 Running Modes

### Production Mode (Full Stack)
```bash
# Start with production config
docker compose -f docker-compose.production.yml up -d

# Services running:
# - Nginx on ports 80/443 (external)
# - App on port 3000 (internal only)
# - PostgreSQL on port 5432 (internal only)
# - Redis on port 6379 (internal only)
```

### Development Mode
```bash
# Start with development config
docker compose up -d

# Services running:
# - App on port 3000 (exposed)
# - PostgreSQL on port 5432 (exposed)
# - Redis on port 6379 (exposed)
# - No Nginx (direct app access)
```

### Local Production Testing
```bash
# Use the production-local script
./scripts/production-local.sh start

# OR use production compose for specific services
docker compose -f docker-compose.production.yml up -d postgres redis
```

## 🔍 Health Checks

### Application Health
```bash
# Through Nginx (production)
curl -k https://localhost/health

# Direct to app (development)
curl http://localhost:3000/health

# Expected response:
# {"status":"healthy","timestamp":"...","uptime":123}
```

### Database Health
```bash
# Check PostgreSQL is running
docker exec lockin-postgres pg_isready -U habituser

# Check connections
docker exec lockin-postgres psql -U habituser -d habittracker_prod -c "SELECT count(*) FROM pg_stat_activity;"
```

### Redis Health
```bash
# Check Redis is running
docker exec lockin-redis redis-cli ping
# Should return: PONG
```

## 🐛 Troubleshooting

### Container won't start
```bash
# Check logs
docker logs lockin-app
docker logs lockin-postgres
docker logs lockin-nginx

# Common issues:
# - Port already in use: Kill existing process or change port
# - Environment variables missing: Check .env.production
# - SSL certificates missing: Generate them (see SSL section)
```

### Database connection errors
```bash
# Verify PostgreSQL is running
docker ps | grep postgres

# Check network connectivity
docker exec lockin-app ping postgres

# Verify credentials
docker exec lockin-postgres psql -U habituser -d habittracker_prod -c "\l"

# Common fixes:
# - Wrong DATABASE_URL format
# - Database not initialized: Run migrations
# - Network issues: Restart Docker
```

### Nginx errors
```bash
# Check Nginx config
docker exec lockin-nginx nginx -t

# View error logs
docker logs lockin-nginx

# Common issues:
# - SSL certificates not found: Generate them
# - Port 80/443 already in use: Stop other services
# - Upstream connection failed: Check app is running
```

### Redis connection errors
```bash
# Verify Redis is running
docker ps | grep redis

# Test connection
docker exec lockin-app redis-cli -h redis ping

# Common fixes:
# - Wrong REDIS_URL format
# - Redis not started: Check docker ps
# - Memory issues: Increase Docker memory limit
```

## 🔐 Security Notes

1. **Never commit .env files** - They contain secrets
2. **Use strong passwords** - Especially for production databases
3. **Rotate credentials regularly** - Especially Twilio tokens
4. **Use real SSL certificates** - Self-signed only for development
5. **Limit exposed ports** - Only expose Nginx 80/443 in production
6. **Set resource limits** - Prevent container resource exhaustion
7. **Regular backups** - Automate database backups

## 📊 Monitoring (Optional)

### Start monitoring stack
```bash
docker compose -f docker-compose.monitoring.yml up -d

# Access:
# - Grafana: http://localhost:3001 (admin/admin)
# - Prometheus: http://localhost:9090
```

### Available Metrics
- Container CPU/Memory usage (cAdvisor)
- PostgreSQL statistics (postgres_exporter)
- Redis statistics (redis_exporter)
- Node.js metrics (prom-client)
- Nginx metrics (stub_status module)

## 🔄 Updates and Maintenance

### Update application code
```bash
# Pull latest code
git pull

# Rebuild image
docker compose -f docker-compose.production.yml build app

# Restart with new image
docker compose -f docker-compose.production.yml up -d app
```

### Update dependencies
```bash
# Update Node.js packages
docker exec lockin-app npm update

# Update Docker images
docker compose -f docker-compose.production.yml pull
docker compose -f docker-compose.production.yml up -d
```

### Database migrations
```bash
# Run pending migrations
docker exec lockin-app npm run migrate

# Rollback last migration
docker exec lockin-app npm run migrate:rollback
```

## 📝 Important Notes

### Container Names
- `lockin-app` - Node.js application
- `lockin-postgres` - PostgreSQL database
- `lockin-redis` - Redis cache
- `lockin-nginx` - Nginx reverse proxy

### Internal Network
Containers communicate using service names, not localhost:
- App connects to database: `postgres:5432`
- App connects to Redis: `redis:6379`
- Nginx proxies to app: `app:3000`

### Data Persistence
All data is stored in Docker volumes:
```bash
# List volumes
docker volume ls | grep lockin

# Backup volume
docker run --rm -v lockin_postgres-data:/data -v $(pwd):/backup alpine tar czf /backup/postgres-backup.tar.gz /data

# Restore volume
docker run --rm -v lockin_postgres-data:/data -v $(pwd):/backup alpine tar xzf /backup/postgres-backup.tar.gz -C /
```

## 🆘 Getting Help

1. Check container logs: `docker logs <container-name>`
2. Verify environment variables: `docker exec <container> env`
3. Test network connectivity: `docker exec <container> ping <other-container>`
4. Check resource usage: `docker stats`
5. Review this guide and troubleshooting section

## 📚 Additional Resources

- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [PostgreSQL Docker Image](https://hub.docker.com/_/postgres)
- [Redis Docker Image](https://hub.docker.com/_/redis)
- [Nginx Docker Image](https://hub.docker.com/_/nginx)
- [Node.js Docker Best Practices](https://github.com/nodejs/docker-node/blob/main/docs/BestPractices.md)