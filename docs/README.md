# Documentation

This directory contains all project documentation.

## Index

- API.md – API reference and endpoints
- ARCHITECTURE.md – System and component architecture
- CONFIGURATION.md – Environment and configuration guide
- DEPLOYMENT.md – Production deployment instructions
- DEVELOPER_GUIDE.md – Contributor setup and workflows
- USER_GUIDE.md – End-user guide for WhatsApp bot
- TROUBLESHOOTING.md – Common issues and diagnostics
- CHANGELOG.md – Project changes and release notes
- PROJECT_CHECKLIST.md – Project hygiene and ops checklists
- SECURITY_AUDIT.md – Security posture and audit notes
- SECURITY_TODO.md – Security follow-ups
- ARCHITECTURE_AUDIT.md – Architecture review notes
- previous_changelogs/ – Historical changelogs
- FAQ.md – Frequently asked questions

---

**Version**: 2.9.0
**Last Updated**: September 7, 2025

