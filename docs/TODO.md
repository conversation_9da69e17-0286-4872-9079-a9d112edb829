
# TODO Architecture

**Version:** 2.9.0
**Last Updated:** September 7, 2025
**Status:** Production-Ready

## SYSTEM OVERVIEW

**Application:** WhatsApp Habit Tracker Bot
**Type:** Production Node.js Application
**Core Features:** Habit tracking via WhatsApp, Payment processing, GDPR compliance
**Architecture Pattern:** Layered architecture with Strategy & Factory patterns

## PAYMENT ARCHITECTURE

| Component | Implementation | Status | Issues | Action Required |
|-----------|---------------|---------|--------|----------------|
| **Primary Provider** | ThriveCart | ✅ Active | None | Maintain as sole provider |
| **Webhook Processing** | Single handler | ✅ Simplified | Was dual | Completed |
| **Access Management** | Code-based system | ✅ Working | None | Document flow |
| **Email Notifications** | Queue-based | ✅ Working | None | Monitor queue health |

## LAYER-BY-LAYER ANALYSIS

### 1. PRESENTATION LAYER (API Endpoints)

**Structure:** `/src/server.js`  
**Total Endpoints:** 11

#### Public Endpoints
- `GET /health` - Health check with optional database status
- `GET /privacy` - Redirect to privacy policy
- `GET /terms` - Redirect to terms of service

#### Webhook Endpoints
- `POST /webhook/whatsapp` - Main WhatsApp message handler (Twilio)
- `POST /webhook/thrivecart` - ThriveCart payment webhook ✅ PRIMARY

#### Authentication
- `POST /admin/login` - Admin authentication endpoint

#### Admin Endpoints (Protected by JWT)
- `GET /admin/compliance/stats` - Compliance statistics dashboard
- `GET /admin/compliance/report` - Generate compliance reports
- `POST /admin/compliance/cleanup` - Execute data retention cleanup
- `GET /admin/compliance/check` - Run compliance verification

### 2. BUSINESS LOGIC LAYER

**Purpose:** Core application logic and request routing  
**Location:** Embedded in `/src/server.js` and controllers

#### Request Flow
1. **Middleware Chain:** Security → CORS → Rate Limiting → Validation → Compliance
2. **Route Matching:** Express router determines handler
3. **Controller Dispatch:** Route to appropriate controller
4. **Response Handling:** Formatted responses with proper headers

#### Key Components
- **Security Headers:** Helmet + custom security policies
- **Rate Limiting:** Global (100/15min) + Per-user limits
- **Validation:** Webhook signature verification (Twilio, payment providers)
- **Compliance Checks:** STOP/START keywords, message windows

### 3. CONTROLLER LAYER

**Location:** `/src/controllers/`  
**Pattern:** Request handling and business logic orchestration

#### Active Controllers

| Controller | Purpose | Status | Dependencies |
|------------|---------|--------|--------------|
| `webhookControllerCompliant.js` | Main WhatsApp message handler | ✅ Primary | unifiedStateMachine, sessionManager |
| `webhookController.js` | Legacy webhook handler | ⚠️ Deprecated | Should migrate to compliant version |
| `thrivecartController.js` | ThriveCart payment processing | ✅ Primary | paymentService, emailService |
| `thrivecart/` | Directory with additional configs | 📁 Support | Contains webhook test utilities |

#### Controller Responsibilities
1. **Request Validation:** Verify webhook signatures and payload structure
2. **Service Orchestration:** Coordinate multiple services for request handling
3. **Error Management:** Catch and format errors appropriately
4. **Response Generation:** Create proper HTTP responses with status codes
5. **Audit Logging:** Track actions for compliance

### 4. SERVICE LAYER

**Location:** `/src/services/`  
**Pattern:** Business logic encapsulation and domain services

#### Core Services

| Service | Purpose | Status | Key Methods |
|---------|---------|--------|-------------|
| `unifiedStateMachine.js` | Central state management | ✅ Primary | processMessage, getState |
| `sessionManager.js` | User session handling | ✅ Active | createSession, validateSession |
| `secureSessionManager.js` | Enhanced session security | ✅ Active | Encrypted session storage |
| `paymentService.js` | Payment processing | ✅ Active | validatePayment, generateAccessCode |
| `subscriptionService.js` | Subscription management | ✅ Active | checkSubscription, updateTier |
| `emailService.js` | Email notifications | ✅ Active | sendPurchaseEmail, queueEmail |
| `complianceService.js` | GDPR compliance | ✅ Active | trackConsent, handleOptOut |
| `complianceAuditService.js` | Audit logging | ✅ Active | logAction, generateReport |
| `dataRetentionService.js` | Data lifecycle | ⚠️ Legacy | Basic retention policies |
| `enhancedRetentionService.js` | Advanced retention | ✅ Active | Automated cleanup, configurable policies |
| `userRightsService.js` | User data rights | ✅ Active | exportUserData, deleteUserData |

#### Service Subdirectories

**`/src/services/email/`**
- `emailQueue.js` - Queue management for async email processing
- `smtpConfig.js` - SMTP configuration and connection pooling
- `emailTemplates.js` - Email template definitions

**`/src/services/handlers/`**
- `habitHandlers.js` - Habit-specific message processing
- `menuHandlers.js` - Menu navigation logic
- `statsHandlers.js` - Statistics and progress calculation
- `onboardingHandlers.js` - New user onboarding flow

### 5. DATA ACCESS LAYER (Models)

**Location:** `/src/models/`  
**Pattern:** Data models and database interaction

| Model | Purpose | Key Fields | Relationships |
|-------|---------|------------|---------------|
| `User.js` | User account management | phone_number, status, timezone | Has many habits |
| `Habit.js` | Habit tracking | name, user_id, progress_percentage | Belongs to user, has many logs |
| `AuditLog.js` | Compliance logging | action, user_identifier_hash, metadata | Standalone audit trail |

#### Model Responsibilities
- **Data Validation:** Ensure data integrity before database operations
- **Business Rules:** Enforce domain-specific constraints
- **Query Methods:** Provide reusable database queries
- **Data Transformation:** Convert between database and application formats

### 6. DATABASE LAYER

**Location:** `/src/db/`  
**Database:** PostgreSQL with connection pooling

| Component | Purpose | Configuration |
|-----------|---------|---------------|
| `connectionPool.js` | Enhanced connection management | Pool size: 20, SSL in production |
| `connection.js` | Legacy single connection | ⚠️ Deprecated, use pool |
| `schema.sql` | Database schema definition | Tables, indexes, constraints |
| `migrate.js` | Schema migration tool | Version control for DB changes |

#### Database Tables
- **users** - User accounts with E.164 phone numbers
- **habits** - User habits (max 5 per user)
- **habit_logs** - Daily completion tracking
- **audit_logs** - GDPR-compliant action logging
- **access_codes** - Payment access management
- **paid_users** - Subscription tracking
- **user_sessions** - Active session management

### 7. MIDDLEWARE LAYER

**Location:** `/src/middleware/`  
**Pattern:** Request/Response interceptors

| Middleware | Purpose | Order | Configuration |
|------------|---------|-------|---------------|
| `auth.js` | JWT authentication | 1st | Admin endpoints only |
| `validation.js` | Webhook signature verification | 2nd | Twilio, ThriveCart secrets |
| `rateLimiter.js` | Request throttling | 3rd | Global: 100/15min, Per-user limits |
| `compliance.js` | GDPR compliance checks | 4th | STOP/START keywords, message windows |
| `enhancedValidation.js` | Advanced input validation | 5th | Schema-based validation |

### 8. UTILITY LAYER

**Location:** `/src/utils/`  
**Purpose:** Shared utilities and helpers

| Utility | Purpose | Usage |
|---------|---------|--------|
| `databaseSecurity.js` | SQL injection prevention | Wraps all database queries |
| `piiHasher.js` | PII anonymization | Hash sensitive data for logs |
| `codeGenerator.js` | Access code generation | Unique codes for payments |
| `sqlIntervalBuilder.js` | SQL interval construction | Date range queries |
| `motivationalQuotes.js` | Content generation | User engagement messages |
| `clearTodayLogs.js` | Maintenance script | Daily log cleanup |
| `resetUser.js` | Admin utility | User state reset |

### 9. CONFIGURATION LAYER

**Location:** `/src/config/`  
**Purpose:** Application configuration and constants

| Component | Purpose | Contents |
|-----------|---------|----------|
| `constants.js` | Application constants | States, limits, timeouts |
| `logger.js` | Winston logger config | Log levels, formats, transports |
| `.env` | Environment variables | Secrets, connection strings |

## ARCHITECTURE PATTERNS

### Strategy Pattern Implementation

**Location:** `/src/core/strategies/`  
**Purpose:** Flexible state machine behavior based on context

```
BaseStrategy (Abstract)
├── CoreStateMachineStrategy (Default conversation flow)
├── PaymentEnforcementStrategy (Requires payment for access)
└── ComplianceStrategy (GDPR & WhatsApp compliance)
```

**Usage:** StateMachineFactory selects strategy based on:
- User payment status
- Compliance requirements
- Feature flags

### Factory Pattern

**Location:** `/src/core/factories/StateMachineFactory.js`  
**Purpose:** Create appropriate state machine instance

```javascript
StateMachineFactory.create(context) → StateMachine instance
```

### Security Wrapper Pattern

**Component:** `DatabaseSecurity` utility  
**Purpose:** Centralized SQL injection prevention

```javascript
// All queries wrapped automatically:
DatabaseSecurity.query(sql, params) → Safe execution
```

### Queue Pattern

**Component:** Email Service  
**Purpose:** Async processing with retry logic

```
Request → Queue → Worker → SMTP → Delivery
           ↓
        Retry on failure
```

## DATA FLOW DIAGRAMS

### User Message Flow
```
WhatsApp User
      ↓
   Twilio API
      ↓
POST /webhook/whatsapp
      ↓
Middleware Pipeline
  ├── Signature Validation
  ├── Rate Limiting
  ├── Compliance Check
  └── Session Validation
      ↓
webhookControllerCompliant
      ↓
unifiedStateMachine
  ├── Strategy Selection
  ├── State Processing
  └── Response Generation
      ↓
   Twilio API
      ↓
WhatsApp User
```

### Payment Processing Flow
```
Customer Purchase
      ↓
ThriveCart Checkout
      ↓
POST /webhook/thrivecart
      ↓
Signature Validation
      ↓
thrivecartController
      ↓
paymentService
  ├── Generate Access Code
  ├── Update Database
  └── Queue Email
      ↓
emailService (Async)
      ↓
Customer Email
```

### State Transitions
```
┌─────────┐
│ LOCKED  │ ← New User
└────┬────┘
     │ Access Code Valid
┌────▼──────────┐
│  ONBOARDING   │
└────┬──────────┘
     │ Setup Complete
┌────▼────┐
│ ACTIVE  │ ← Full Access
└─────────┘
```

### Session Lifecycle
```
New Message → No Session? → Create (30min TTL)
     ↓              ↓
Has Session → Update Activity → Process
     ↓
Inactive 30min → Session Expires → Cleanup
```

---

# Create Comprehensive Testing Suite

---

# Phase 1 Security Audit - COMPLETE (Sept 6, 2025)

All Phase 1 security issues have been resolved:
- ✅ DatabaseSecurity utility applied to all services
- ✅ JWT authentication middleware integrated for admin endpoints  
- ✅ Old state machine files removed
- ✅ Enhanced database connection pooling implemented
- ✅ 13 of 14 security issues fully resolved

## Remaining Infrastructure Issue (Deferred)

### 🟡 LOW-02: Database SSL Certificate Configuration [INFRASTRUCTURE-LEVEL]
**Severity:** Low  
**Location:** `src/db/connectionPool.js`

**Issue:** Basic SSL configuration for database connections
```javascript
ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
```

**Risk:** Potential man-in-the-middle attacks on database connections.

**Recommendation:**
- Use proper SSL certificate validation
- Implement connection encryption with verified certificates
- Monitor database connections for security events

**Status:** DEFERRED - Requires infrastructure-level changes and SSL certificate management. This is not a code vulnerability but an infrastructure configuration that needs to be addressed at the deployment level.

**Next Steps:** Address during infrastructure upgrade or when migrating to managed database service with built-in SSL certificate management.
