# WhatsApp Testing Guide

This guide provides comprehensive instructions for testing the Lock In Habit Tracker Bot with WhatsApp.

## Prerequisites

1. **Twilio Account**: Sign up at [twilio.com](https://www.twilio.com) with WhatsApp Business API configured
2. **ngrok Account**: Sign up at [ngrok.com](https://ngrok.com) for local webhook testing
3. **Node.js**: v18+ installed
4. **PostgreSQL**: Running locally or via Docker with migrations applied
5. **Redis**: Running locally or via Docker (optional for sessions)

## Step-by-Step Testing Process

### 1. Start Your Local Server

```bash
npm run dev
```

Verify it's running:
```bash
curl http://localhost:3000/health
```

### 2. Set Up ngrok Tunnel

```bash
# Install ngrok (if not already installed)
# On macOS:
brew install ngrok

# On Linux:
snap install ngrok

# Configure ngrok with your auth token
ngrok config add-authtoken YOUR_AUTH_TOKEN

# Start ngrok tunnel to expose local port 3000
ngrok http 3000
```

Note your HTTPS URL provided (e.g., `https://3ccc32ffe2ad.ngrok-free.app`)

### 3. Configure Twilio Webhook

#### For Production WhatsApp Business API:
1. Go to [Twilio Console](https://console.twilio.com/)
2. Navigate to: **Messaging → WhatsApp Senders → Your WhatsApp sender**
3. Set webhook URL to: `https://YOUR_NGROK_URL.ngrok-free.app/webhook/whatsapp`
4. Method: **POST**
5. Save configuration

#### For Twilio WhatsApp Sandbox (Testing):
1. Navigate to **Messaging → Try it out → Send a WhatsApp message**
2. Set **When a message comes in**: `https://YOUR_NGROK_URL.ngrok-free.app/webhook/whatsapp`
3. Method: **POST**
4. **Status Callback URL**: (optional) same URL for delivery receipts

To join the sandbox from your test phone:
1. Send a WhatsApp message to ******** 523 8886** (Twilio Sandbox number)
2. Message format: `join [your-sandbox-keyword]`
3. You'll receive a confirmation message

### 4. Database Setup

Run migrations if not already done:
```bash
NODE_ENV=production node src/db/migrate.js
```

### 5. Testing Configuration

For local testing with ngrok, Twilio signature validation is temporarily disabled in `src/app.js`:

```javascript
app.post('/webhook/whatsapp',
  globalRateLimiter,
  perUserRateLimiter,
  // IMPORTANT: Re-enable these for production!
  // validateWebhook,  // Validates request structure
  // webhookController.verifyWebhook,  // Validates Twilio signature
  ...
);
```

**⚠️ IMPORTANT**: Re-enable these middlewares before deploying to production!

### 6. Send Test Messages

1. **First message**: Send "START" to opt-in
2. **After opt-in**: Send any message to interact with the bot

Expected response:
```
📱 MAIN MENU

① Log Today's Habits
② Set/Edit Habits
③ View Progress
④ Stats & Insights
⑤ Settings

Reply with a number (1-5)
```

### 7. Monitor Activity

Check ngrok traffic dashboard:
```bash
# Visit in browser
http://localhost:4040
```
This shows:
- All incoming requests
- Request/response details  
- Ability to replay requests for debugging

Check ngrok requests via API:
```bash
curl -s http://localhost:4040/api/requests/http | python3 -m json.tool
```

View server logs:
```bash
# Using npm dev logs
tail -f /tmp/server.log

# Or production logs
tail -f logs/production/app.log

# Or with the production script
./scripts/production-local.sh logs
```

Database verification:
```bash
# Connect to PostgreSQL
psql -U habituser -d habittracker_prod

# Check users
SELECT * FROM users WHERE phone LIKE '%YOUR_PHONE_NUMBER%';

# Check habits
SELECT * FROM habits WHERE user_id = (
  SELECT id FROM users WHERE phone LIKE '%YOUR_PHONE_NUMBER%'
);
```

## Common Issues and Solutions

### Issue: 403 Forbidden Error

**Cause**: Twilio signature validation failing with ngrok.

**Solution**: 
- For testing: Temporarily disable validation (as shown above)
- For production: Ensure webhook URL matches exactly what Twilio expects

### Issue: Database Column Missing Errors

**Cause**: Missing database migrations.

**Solution**:
```bash
NODE_ENV=production node src/db/migrate.js
```

If specific columns are still missing, check and apply individual migrations:
```bash
ls src/db/migrations/
```

### Issue: 404 Not Found

**Cause**: Wrong webhook endpoint configured.

**Solution**: Use `/webhook/whatsapp` (not `/webhook/twilio`)

## Production Deployment Checklist

Before deploying to production:

1. ✅ Re-enable Twilio signature validation in `src/app.js`
2. ✅ Update webhook URL in Twilio Console to production URL
3. ✅ Ensure all environment variables are set correctly
4. ✅ Run database migrations on production database
5. ✅ Test with production Twilio credentials
6. ✅ Enable SSL/HTTPS for production webhook endpoint

## Environment Variables

Key variables for WhatsApp integration:

```env
# Twilio Configuration
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=whatsapp:+**********

# Note: Webhook URLs are configured directly in Twilio Console, not in .env
```

## Testing Workflow

1. **Onboarding Flow**
   - New users start in LOCKED state
   - Send any message to trigger welcome
   - Follow prompts to set up habits
   - Test timezone selection
   - Verify habit creation

2. **Main Menu Navigation**
   - Users can navigate using numbers 1-5
   - Test each menu option thoroughly

3. **Habit Management**
   - Option 2 allows setting up to 5 habits  
   - Test habit editing and deletion
   - Verify habit limits

4. **Daily Logging**
   - Option 1 for marking habits complete
   - Test "Log Progress" with multiple habits
   - Verify progress tracking and streaks

5. **Progress Tracking**
   - Option 3 shows completion stats
   - Check weekly/monthly summaries
   - Verify streak calculations

6. **Settings Management**
   - Test timezone changes
   - Data export requests
   - Profile updates

7. **GDPR Compliance**
   - "STOP" to opt-out
   - "START" to opt-in
   - Test data export/deletion requests

## Debugging Tips

- Check webhook URL is correct in Twilio Console
- Verify ngrok is running and forwarding to correct port
- Ensure database has all required columns
- Monitor server logs for detailed error messages
- Use ngrok dashboard at http://localhost:4040 for request inspection

## Support Commands

Users can send these commands:
- `STOP` - Opt out of messages
- `START` - Opt back in
- `HELP` - Get support information
- `EXPORT MY DATA` - GDPR data export
- `DELETE MY DATA` - GDPR account deletion

## Useful Testing Commands

```bash
# Test webhook directly (bypass WhatsApp)
curl -X POST https://YOUR_NGROK_URL/webhook/whatsapp \
  -d "From=whatsapp:+**********&Body=Test" \
  -H "Content-Type: application/x-www-form-urlencoded"

# Health check
curl http://localhost:3000/health

# Using production scripts
./scripts/production-local.sh start    # Start all services
./scripts/production-local.sh stop     # Stop all services
./scripts/production-local.sh restart  # Restart services
./scripts/production-local.sh status   # Check status
./scripts/production-local.sh logs     # View logs
```

## Testing Checklist

- [ ] Environment setup complete
- [ ] ngrok tunnel established
- [ ] Twilio webhook configured
- [ ] Sandbox joined (if using sandbox)
- [ ] Welcome message received
- [ ] Onboarding flow completed
- [ ] Habits created successfully
- [ ] Progress logging works
- [ ] Progress viewing works
- [ ] STOP/START commands work
- [ ] Session management works (30-min timeout)
- [ ] Error handling tested
- [ ] Database migrations applied
- [ ] Rate limiting tested (100 req/15 min)