# Developer Guide

**Lock In - WhatsApp Habit Tracker Bot**
**Version**: 2.9.0
**Internal Development Documentation**
**Last Updated**: September 7, 2025

---

## Table of Contents

- [Development Setup](#development-setup)
- [Project Structure](#project-structure)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Database Development](#database-development)
- [Debugging & Troubleshooting](#debugging--troubleshooting)
- [Development Standards](#development-standards)

---

## Development Setup

### Prerequisites

- **Node.js 18+** (LTS recommended)
- **PostgreSQL 15+** 
- **Docker & Docker Compose** (optional but recommended)
- **Git** for version control
- **Code Editor** (VS Code recommended)

### Quick Setup

```bash
# 1. Clone the private repository
git clone https://github.com/richvieren/lockin.git
cd lockin

# 2. Install dependencies
npm install

# 3. Copy environment template (create if doesn't exist)
cp .env.example .env || touch .env

# 4. Configure environment variables (see Configuration section)
nano .env

# 5. Start PostgreSQL (Docker method)
docker-compose up -d postgres

# 6. Run database migrations
npm run migrate

# 7. Start development server
npm run dev
```

### Environment Configuration

Create `.env` file with development settings:

```bash
# Database
DATABASE_URL=postgresql://habituser:password@localhost:5432/habittracker

# Twilio (for testing - use Twilio test credentials)
TWILIO_ACCOUNT_SID=your_test_account_sid
TWILIO_AUTH_TOKEN=your_test_auth_token
TWILIO_PHONE_NUMBER=+***********

# Email (optional for development)
SMTP_HOST=smtp.mailtrap.io
SMTP_PORT=2525
SMTP_USER=your_mailtrap_user
SMTP_PASS=your_mailtrap_pass
EMAIL_FROM=<EMAIL>

# Development flags
NODE_ENV=development
LOG_LEVEL=debug
PAYMENT_TEST_MODE=true
ADMIN_ACCESS=true

# Security (generate random strings)
JWT_SECRET=your_jwt_secret_for_development
ADMIN_PASSWORD=admin123
```

### Development Tools

#### Recommended VS Code Extensions
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Thunder Client**: API testing
- **PostgreSQL**: Database management
- **Docker**: Container management

#### Useful npm Scripts
```bash
# Development
npm run dev          # Start with nodemon (hot reload)
npm start           # Start production mode
npm test            # Run all tests
npm run test:watch  # Run tests in watch mode

# Database
npm run migrate     # Run database migrations
npm run migrate:rollback  # Rollback last migration

# Utilities
npm run lint        # Check code style
npm run format      # Format code with Prettier
```

---

## Project Structure

### Directory Overview

```
lockin/
├── src/                    # Source code
│   ├── config/            # Configuration files
│   ├── controllers/       # HTTP request handlers
│   ├── core/             # Core business logic (Strategy pattern)
│   ├── db/               # Database connection & migrations
│   ├── middleware/       # Express middleware
│   ├── models/           # Data models
│   ├── routes/           # Route handlers (test endpoints)
│   ├── services/         # Business services
│   ├── templates/        # Email templates
│   ├── utils/            # Utility functions
│   ├── app.js            # Express app configuration
│   └── server.js         # Application entry point
├── tests/                 # Test files
│   ├── unit/             # Unit tests
│   ├── integration/      # Integration tests
│   ├── e2e/              # End-to-end tests
│   ├── fixtures/         # Test data
│   └── helpers/          # Test utilities
├── docs/                 # Documentation
│   └── previous_changelogs/  # Historical changelogs
├── docker/               # Docker configurations
│   ├── app/              # Application Dockerfiles
│   ├── nginx/            # Nginx configuration
│   ├── postgres/         # PostgreSQL setup
│   └── monitoring/       # Prometheus & Grafana
├── public/               # Static files
│   └── legal/            # Legal documents (privacy, terms)
├── scripts/              # Utility scripts
│   ├── backup.sh         # Database backup
│   ├── debug/            # Debug utilities
│   └── monitoring/       # Monitoring scripts
├── Makefile              # Deployment automation
├── openapi.yaml          # API specification
└── docker-compose*.yml   # Docker configurations
```

### Key Components

#### Controllers (`src/controllers/`)
- **webhookControllerCompliant.js**: Main WhatsApp message handler (current)
- **webhookController.js**: Legacy webhook handler (deprecated)
- **thrivecartController.js**: ThriveCart payment webhooks

#### Services (`src/services/`)
- **unifiedStateMachine.js**: Core conversation logic with Strategy pattern
- **paymentService.js**: Subscription management
- **emailService.js**: Email automation with queue processing
- **sessionManager.js**: User session handling (30-minute timeout)
- **secureSessionManager.js**: Enhanced session security
- **complianceService.js**: GDPR compliance
- **complianceAuditService.js**: Audit logging
- **enhancedRetentionService.js**: Data retention automation

#### Models (`src/models/`)
- **User.js**: User data operations
- **Habit.js**: Habit CRUD and analytics
- **AuditLog.js**: Compliance logging

### Architecture Patterns

#### Strategy Pattern (State Machine)
```javascript
// Core strategy for conversation logic
class CoreStateMachineStrategy {
  async process(context) {
    // Handle basic conversation flow
    return context;
  }
}

// Payment enforcement decorator
class PaymentEnforcementStrategy {
  async process(context) {
    // Check subscription status
    return context;
  }
}

// Compliance wrapper
class ComplianceStrategy {
  async process(context) {
    // Ensure GDPR compliance
    return context;
  }
}
```

#### Repository Pattern
```javascript
// Abstract database operations
class UserRepository {
  static async findOrCreate(phone) {
    // Database logic abstracted
  }
}
```

---

## Development Workflow

### Git Workflow

#### Branch Naming Convention
- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `hotfix/description` - Critical fixes
- `refactor/description` - Code refactoring
- `docs/description` - Documentation updates

#### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

**Examples:**
```bash
feat(auth): add JWT authentication for admin endpoints
fix(webhook): handle malformed Twilio signatures
docs(api): update webhook endpoint documentation
test(state-machine): add unit tests for habit logging
```

### Development Process

#### 1. Setup Development Environment
```bash
# Start development stack
docker-compose up -d postgres
npm run dev

# In another terminal, run tests
npm run test:watch
```

#### 2. Make Changes
- Write code following coding standards
- Add/update tests for new functionality
- Update documentation if needed
- Test locally before committing

#### 3. Testing Checklist
```bash
# Run all tests
npm test

# Check test coverage
npm test -- --coverage

# Lint code
npm run lint

# Test database migrations
npm run migrate:rollback
npm run migrate
```

#### 4. Submit Changes
```bash
# Create feature branch
git checkout -b feature/your-feature

# Commit changes
git add .
git commit -m "feat(scope): description"

# Push and create PR
git push origin feature/your-feature
```

---

## Coding Standards

### JavaScript Style Guide

#### General Principles
- **ES6+**: Use modern JavaScript features
- **Async/Await**: Prefer over callbacks and raw promises
- **Const/Let**: No `var` declarations
- **Arrow Functions**: For short functions and callbacks
- **Template Literals**: For string interpolation

#### Code Examples

**Good:**
```javascript
// Use async/await
async function createUser(phone, name) {
  try {
    const user = await User.create({ phone, name });
    logger.info('User created', { userId: user.id });
    return user;
  } catch (error) {
    logger.error('Failed to create user', { error: error.message });
    throw error;
  }
}

// Use template literals
const message = `Welcome ${user.name}! Your streak is ${streak} days.`;

// Use destructuring
const { phone, name, timezone } = req.body;
```

**Avoid:**
```javascript
// Don't use callbacks when async/await is available
function createUser(phone, name, callback) {
  User.create({ phone, name }, (err, user) => {
    if (err) return callback(err);
    callback(null, user);
  });
}

// Don't use string concatenation
const message = 'Welcome ' + user.name + '! Your streak is ' + streak + ' days.';
```

### Error Handling

#### Standard Error Pattern
```javascript
async function serviceMethod() {
  try {
    // Business logic
    const result = await someOperation();
    return result;
  } catch (error) {
    logger.error('Operation failed', { 
      error: error.message,
      stack: error.stack 
    });
    throw error; // Re-throw for caller to handle
  }
}
```

#### Database Error Handling
```javascript
async function findUser(phone) {
  try {
    const result = await pool.query(
      'SELECT * FROM users WHERE phone = $1',
      [phone]
    );
    return result.rows[0];
  } catch (error) {
    logger.error('Database query failed', { 
      query: 'findUser',
      phone: '[REDACTED]',
      error: error.message 
    });
    throw new Error('Database operation failed');
  }
}
```

### Security Guidelines

#### Input Validation
```javascript
const Joi = require('joi');

const phoneSchema = Joi.string()
  .pattern(/^\+[1-9]\d{1,14}$/)
  .required();

const { error, value } = phoneSchema.validate(phone);
if (error) {
  throw new ValidationError('Invalid phone number format');
}
```

#### SQL Injection Prevention
```javascript
// Good - parameterized queries
const result = await pool.query(
  'SELECT * FROM users WHERE phone = $1 AND status = $2',
  [phone, status]
);

// Bad - string concatenation
const result = await pool.query(
  `SELECT * FROM users WHERE phone = '${phone}'`
);
```

#### PII Protection
```javascript
// Redact sensitive data in logs
logger.info('User login attempt', {
  phone: '[REDACTED]',
  ip: req.ip,
  userAgent: req.get('User-Agent')
});
```

---

## Testing Guidelines

### Test Structure

#### Unit Tests (`tests/unit/`)
```javascript
const { describe, test, expect } = require('@jest/globals');
const StateMachine = require('../../src/services/stateMachine');

describe('StateMachine', () => {
  describe('processMessage', () => {
    test('should handle menu command', async () => {
      const user = { id: 1, current_state: 'ACTIVE' };
      const message = 'menu';
      
      const result = await StateMachine.processMessage(user, message);
      
      expect(result.message).toContain('MAIN MENU');
      expect(result.newState).toBe('MAIN_MENU');
    });
  });
});
```

#### Integration Tests (`tests/integration/`)
```javascript
const request = require('supertest');
const app = require('../../src/app');

describe('Webhook Endpoints', () => {
  test('POST /webhook/whatsapp should process message', async () => {
    const response = await request(app)
      .post('/webhook/whatsapp')
      .send({
        Body: 'menu',
        From: '+1234567890'
      })
      .expect(200);
      
    expect(response.text).toContain('<Message>');
  });
});
```

### Test Data Management

#### Fixtures (`tests/fixtures/`)
```javascript
// userFixtures.js
module.exports = {
  activeUser: {
    id: 1,
    phone: '+1234567890',
    status: 'ACTIVE',
    current_state: 'MAIN_MENU'
  },
  
  lockedUser: {
    id: 2,
    phone: '+0987654321',
    status: 'LOCKED',
    current_state: 'MAIN_MENU'
  }
};
```

#### Test Helpers (`tests/helpers/`)
```javascript
// dbHelper.js
const pool = require('../../src/db/connection');

async function clearDatabase() {
  await pool.query('TRUNCATE users, habits, habit_logs CASCADE');
}

async function createTestUser(userData) {
  const result = await pool.query(
    'INSERT INTO users (phone, status) VALUES ($1, $2) RETURNING *',
    [userData.phone, userData.status]
  );
  return result.rows[0];
}

module.exports = { clearDatabase, createTestUser };
```

### Running Tests

```bash
# Run all tests
npm test

# Run specific test file
npm test -- tests/unit/stateMachine.test.js

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm run test:watch

# Run only integration tests
npm test -- tests/integration/
```

---

## Database Development

### Migration System

#### Creating Migrations
```bash
# Create new migration file
touch database/migrations/$(date +%Y%m%d_%H%M%S)_add_new_feature.sql
```

#### Migration Template
```sql
-- Migration: Add new feature
-- Created: 2025-01-28

-- Up migration
BEGIN;

CREATE TABLE new_feature (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  feature_data JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_new_feature_user_id ON new_feature(user_id);

COMMIT;

-- Down migration (for rollback)
-- DROP TABLE new_feature;
```

#### Running Migrations
```bash
# Apply migrations
npm run migrate

# Rollback last migration
npm run migrate:rollback

# Check migration status
npm run migrate:status
```

### Database Best Practices

#### Query Optimization
```javascript
// Use indexes for frequent queries
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_habit_logs_date ON habit_logs(log_date);

// Use EXPLAIN to analyze queries
const result = await pool.query('EXPLAIN ANALYZE SELECT ...');
```

#### Connection Management
```javascript
// Use connection pooling
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000
});

// Always handle connection errors
pool.on('error', (err) => {
  logger.error('Database pool error', { error: err.message });
});
```

---

## Debugging & Troubleshooting

### Logging

#### Development Logging
```javascript
const logger = require('./config/logger');

// Different log levels
logger.debug('Detailed debug information');
logger.info('General information');
logger.warn('Warning message');
logger.error('Error occurred', { error: error.message });
```

#### Debugging State Machine
```javascript
// Add debug logs to state transitions
logger.debug('State transition', {
  userId: user.id,
  fromState: user.current_state,
  toState: newState,
  message: message.substring(0, 50)
});
```

### Common Issues

#### Database Connection Issues
```bash
# Check PostgreSQL status
docker-compose ps postgres

# View database logs
docker-compose logs postgres

# Connect to database directly
docker-compose exec postgres psql -U habituser -d habittracker
```

#### Webhook Testing
```bash
# Use ngrok for local webhook testing
npx ngrok http 3000

# Test webhook with curl
curl -X POST http://localhost:3000/webhook/whatsapp \
  -d "Body=test&From=%2B1234567890"
```

#### Memory Leaks
```bash
# Monitor memory usage
docker stats

# Profile Node.js memory
node --inspect src/server.js
```

### Development Tools

#### Database GUI
```bash
# Start pgAdmin (optional)
docker-compose --profile debug up -d pgadmin

# Access at http://localhost:5050
# Email: <EMAIL>
# Password: admin
```

#### API Testing
Use Thunder Client or Postman to test endpoints:
```json
{
  "method": "POST",
  "url": "http://localhost:3000/webhook/whatsapp",
  "headers": {
    "Content-Type": "application/x-www-form-urlencoded"
  },
  "body": "Body=menu&From=%2B1234567890"
}
```

---

## Development Standards

### Internal Development Process

1. **Read the documentation** thoroughly
2. **Follow coding standards** and test requirements
3. **Ensure security best practices** are followed
4. **Maintain code quality** and test coverage

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No security vulnerabilities introduced
- [ ] Performance impact considered
- [ ] Backward compatibility maintained

### Note on Proprietary Software

This is proprietary software owned by Rich Vieren. All development is internal only. No external contributions are accepted.

### Getting Help

- **GitHub Issues**: Report bugs and request features
- **GitHub Discussions**: Ask questions and discuss ideas
- **Email**: <EMAIL> for private matters

---

**Happy coding!** 🚀  
**Last Updated**: January 28, 2025
