# CI/CD Implementation Plan for GitHub Actions with DigitalOcean Droplet

## Overview
This document outlines the complete plan for implementing CI/CD using GitHub Actions (free tier) with deployment to a DigitalOcean droplet.

## Prerequisites
- [ ] GitHub repository access with admin permissions
- [ ] DigitalOcean droplet with SSH access
- [ ] Node.js 20+ installed on droplet
- [ ] PM2 installed globally on droplet
- [ ] PostgreSQL database on droplet
- [ ] Git installed on droplet

## Phase 1: GitHub Actions Setup

### 1.1 Create Workflow Directory Structure
- [ ] Create `.github/workflows/` directory
- [ ] Add `.github/dependabot.yml` for dependency updates (optional)

### 1.2 Configure GitHub Secrets
Navigate to Repository Settings > Secrets and variables > Actions

Required secrets:
- [ ] `DROPLET_HOST` - DigitalOcean droplet IP address
- [ ] `DROPLET_USERNAME` - SSH username (usually `root` or custom user)
- [ ] `DROPLET_SSH_KEY` - Private SSH key for authentication
- [ ] `DROPLET_PORT` - SSH port (default 22)

Test environment secrets:
- [ ] `TEST_DATABASE_URL` - Test database connection string
- [ ] `TWILIO_AUTH_TOKEN` - For webhook validation in tests
- [ ] `THRIVECART_SECRET` - For payment webhook tests
- [ ] `JWT_SECRET` - For JWT tests

## Phase 2: CI Pipeline (Testing & Validation)

### 2.1 Test Workflow (`.github/workflows/test.yml`)
```yaml
name: CI Tests
on:
  pull_request:
    branches: [master, security]
  push:
    branches: [master, security]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linter
        run: npm run lint
      
      - name: Run tests with coverage
        env:
          DATABASE_URL: postgresql://postgres:testpass@localhost:5432/testdb
          NODE_ENV: test
          TWILIO_AUTH_TOKEN: ${{ secrets.TWILIO_AUTH_TOKEN }}
          THRIVECART_SECRET: ${{ secrets.THRIVECART_SECRET }}
          JWT_SECRET: ${{ secrets.JWT_SECRET }}
        run: npm test -- --coverage
      
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          files: ./coverage/lcov.info
      
      - name: Check for vulnerabilities
        run: npm audit --audit-level=high
```

### 2.2 Build Validation
- [ ] Validate Node.js compatibility
- [ ] Check for missing dependencies
- [ ] Verify environment variable configuration
- [ ] Test database migration scripts

## Phase 3: CD Pipeline (Deployment)

### 3.1 Deployment Workflow (`.github/workflows/deploy.yml`)
```yaml
name: Deploy to Production
on:
  push:
    branches: [master]
  workflow_dispatch:

jobs:
  test:
    uses: ./.github/workflows/test.yml
    secrets: inherit
  
  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to DigitalOcean Droplet
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_HOST }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          port: ${{ secrets.DROPLET_PORT }}
          script: |
            cd /var/www/lockin
            
            # Backup current version
            cp -r . ../lockin-backup-$(date +%Y%m%d-%H%M%S)
            
            # Pull latest changes
            git fetch origin master
            git reset --hard origin/master
            
            # Install dependencies
            npm ci --production
            
            # Run migrations
            NODE_ENV=production npm run migrate
            
            # Restart application
            pm2 restart lockin --update-env
            
            # Health check
            sleep 5
            curl -f http://localhost:3000/health || exit 1
      
      - name: Notify deployment status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo " Deployment successful"
          else
            echo "L Deployment failed"
          fi
```

### 3.2 Staging Deployment (Optional)
- [ ] Create `.github/workflows/deploy-staging.yml`
- [ ] Deploy to staging environment on push to `staging` branch
- [ ] Use different environment variables and database

## Phase 4: Deployment Scripts

### 4.1 Server Deployment Script (`scripts/deploy.sh`)
Location: On the DigitalOcean droplet at `/var/www/lockin/scripts/deploy.sh`

```bash
#!/bin/bash
set -e

# Configuration
APP_DIR="/var/www/lockin"
BACKUP_DIR="/var/backups/lockin"
PM2_APP_NAME="lockin"
MAX_BACKUPS=5

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}Starting deployment...${NC}"

# Create backup
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
BACKUP_PATH="$BACKUP_DIR/backup-$TIMESTAMP"
mkdir -p "$BACKUP_PATH"
cp -r "$APP_DIR"/* "$BACKUP_PATH/"
echo -e "${GREEN} Backup created at $BACKUP_PATH${NC}"

# Pull latest code
cd "$APP_DIR"
git fetch origin master
CURRENT_COMMIT=$(git rev-parse HEAD)
git reset --hard origin/master
NEW_COMMIT=$(git rev-parse HEAD)

if [ "$CURRENT_COMMIT" == "$NEW_COMMIT" ]; then
    echo -e "${YELLOW}No new changes to deploy${NC}"
    exit 0
fi

# Install dependencies
echo "Installing dependencies..."
npm ci --production

# Run database migrations
echo "Running migrations..."
NODE_ENV=production npm run migrate

# Restart application with PM2
echo "Restarting application..."
pm2 restart "$PM2_APP_NAME" --update-env

# Wait for application to start
sleep 5

# Health check
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo -e "${GREEN} Deployment successful!${NC}"
    echo "Deployed commit: $NEW_COMMIT"
    
    # Clean old backups (keep last 5)
    cd "$BACKUP_DIR"
    ls -t | tail -n +$((MAX_BACKUPS + 1)) | xargs -r rm -rf
else
    echo -e "${RED} Health check failed! Rolling back...${NC}"
    
    # Rollback
    rm -rf "$APP_DIR"/*
    cp -r "$BACKUP_PATH"/* "$APP_DIR/"
    cd "$APP_DIR"
    npm ci --production
    pm2 restart "$PM2_APP_NAME"
    
    echo -e "${RED}Rollback completed${NC}"
    exit 1
fi
```

### 4.2 Rollback Script (`scripts/rollback.sh`)
Location: On the DigitalOcean droplet at `/var/www/lockin/scripts/rollback.sh`

```bash
#!/bin/bash
set -e

APP_DIR="/var/www/lockin"
BACKUP_DIR="/var/backups/lockin"
PM2_APP_NAME="lockin"

# Get the latest backup
LATEST_BACKUP=$(ls -t "$BACKUP_DIR" | head -1)

if [ -z "$LATEST_BACKUP" ]; then
    echo "L No backup found!"
    exit 1
fi

echo "Rolling back to: $LATEST_BACKUP"

# Restore from backup
rm -rf "$APP_DIR"/*
cp -r "$BACKUP_DIR/$LATEST_BACKUP"/* "$APP_DIR/"

# Reinstall dependencies
cd "$APP_DIR"
npm ci --production

# Restart application
pm2 restart "$PM2_APP_NAME"

echo " Rollback completed successfully"
```

### 4.3 PM2 Configuration
Create `ecosystem.config.js` in project root:

```javascript
module.exports = {
  apps: [{
    name: 'lockin',
    script: './src/server.js',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    max_memory_restart: '500M',
    min_uptime: '10s',
    max_restarts: 10,
    autorestart: true,
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    wait_ready: true,
    listen_timeout: 10000,
    kill_timeout: 5000
  }]
};
```

## Phase 5: Monitoring & Notifications

### 5.1 Workflow Status Badges
Add to `README.md`:
```markdown
![CI Tests](https://github.com/USERNAME/REPO/workflows/CI%20Tests/badge.svg)
![Deploy](https://github.com/USERNAME/REPO/workflows/Deploy%20to%20Production/badge.svg)
```

### 5.2 Deployment Notifications
- [ ] Configure GitHub Actions notifications in repository settings
- [ ] Optional: Add Slack/Discord webhook for deployment status
- [ ] Optional: Email notifications on failure

### 5.3 Health Monitoring
- [ ] Implement comprehensive health check endpoint
- [ ] Add database connectivity check
- [ ] Add Redis connectivity check
- [ ] Monitor critical services

## Phase 6: Documentation

### 6.1 Setup Instructions
Document in `docs/CI-CD.md`:
- [ ] How to configure GitHub secrets
- [ ] How to set up droplet for deployments
- [ ] How to configure PM2
- [ ] How to set up SSH keys

### 6.2 Deployment Process
- [ ] Manual deployment instructions
- [ ] Automatic deployment flow
- [ ] Rollback procedures
- [ ] Troubleshooting guide

### 6.3 Security Considerations
- [ ] SSH key management
- [ ] Secret rotation schedule
- [ ] Access control
- [ ] Audit logging

## Implementation Checklist

### Immediate Actions (Required)
1. [ ] Create `.github/workflows/` directory
2. [ ] Add test workflow (`.github/workflows/test.yml`)
3. [ ] Add deployment workflow (`.github/workflows/deploy.yml`)
4. [ ] Configure GitHub secrets
5. [ ] Create deployment script on droplet
6. [ ] Set up PM2 on droplet
7. [ ] Test deployment pipeline

### Short-term Actions (Recommended)
1. [ ] Add staging environment
2. [ ] Implement rollback script
3. [ ] Add deployment notifications
4. [ ] Create comprehensive documentation
5. [ ] Add workflow badges to README

### Long-term Actions (Optional)
1. [ ] Add Dependabot for dependency updates
2. [ ] Implement blue-green deployments
3. [ ] Add performance monitoring
4. [ ] Create deployment dashboard
5. [ ] Implement automatic rollback on metrics

## Cost Analysis
- **GitHub Actions**: Free tier includes 2,000 minutes/month for private repos
- **Estimated usage**: ~50 minutes/month for basic CI/CD
- **Cost**: $0 (well within free tier)

## Security Notes
1. Never commit secrets to the repository
2. Use GitHub's encrypted secrets for sensitive data
3. Rotate SSH keys regularly
4. Limit deployment access to specific branches
5. Use least-privilege principle for droplet user

## Troubleshooting

### Common Issues
1. **SSH connection fails**: Check SSH key format and permissions
2. **Health check fails**: Ensure application starts correctly and port is accessible
3. **Migrations fail**: Check database connection and migration scripts
4. **PM2 issues**: Verify PM2 is installed globally and ecosystem config is correct

### Debug Commands
```bash
# Check GitHub Actions logs
# View in GitHub UI: Actions tab

# On droplet
pm2 status
pm2 logs lockin
pm2 monit

# Check application logs
tail -f /var/www/lockin/logs/production/app.log

# Test health endpoint
curl http://localhost:3000/health
```

## Next Steps
1. Review and approve this plan
2. Configure GitHub secrets
3. Create workflow files
4. Test with a non-production branch first
5. Deploy to production

---

**Status**: Planning Complete - Ready for Implementation
**Last Updated**: 2025-09-07