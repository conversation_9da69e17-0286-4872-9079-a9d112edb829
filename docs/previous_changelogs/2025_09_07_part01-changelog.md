# Changelog

All notable changes to the **Lock In - WhatsApp Habit Tracker Bot** project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

# Previous Changelogs: `docs/previous_changelogs/`

---

## [2.13.0] - 2025-09-07

### Added
- **Test Suite Improvements**
  - Created comprehensive test for `webhookControllerCompliant.js` covering WhatsApp message handling
  - Added full test coverage for `sessionManager.js` including session expiration and rate limiting
  - Implemented `paymentService.test.js` with ThriveCart integration and access code validation
  - Created `menuHandlers.test.js` for menu navigation and user interaction flows
  - Added test patterns and mocking strategies for consistent test implementation

### Fixed
- **Test Suite Repairs**
  - Fixed `sqlIntervalBuilder.test.js` - Resolved 3 failing tests related to interval validation
  - Fixed `emailService.test.js` - Resolved timeout errors by correcting async/await handling
  - Fixed `User.test.js` - Updated 10 tests to match current API (error messages, removed unlockWithCode)
  - Corrected test expectations to match actual implementation behavior

### Changed
- **Test Coverage Status**
  - Current coverage: 25.45% statements, 18.98% branches, 31.32% functions, 25.35% lines
  - Test pass rate: 55.4% (210 passing, 169 failing out of 379 tests)
  - Established 80% coverage target for all metrics
  - Documented comprehensive test improvement roadmap

### Documentation
- **Testing Documentation Updates**
  - Updated TESTING.md with current coverage metrics and test status
  - Added detailed test improvement priorities and roadmap
  - Documented new test files and their coverage areas
  - Added troubleshooting guides for common test failures

---

## [2.12.0] - 2025-09-07

### Changed
- **Docker Infrastructure Optimization**
  - Consolidated from 4 Docker Compose files to 3 for better maintainability
  - Removed redundant `docker-compose.local-prod.yml` file
  - Standardized naming convention to "lockin" across all configurations
  - Separated development and production environments clearly
  
- **Docker Compose Improvements**
  - `docker-compose.yml` now properly configured for development with NODE_ENV=development
  - Added Redis service to development configuration
  - Moved development tools (pgAdmin, Mailhog) behind profiles for optional loading
  - Fixed database naming consistency (lockin_dev, lockin_prod)
  
- **Dockerfile Optimization**
  - Optimized production Dockerfile with proper 3-stage build (deps/builder/production)
  - Reduced image size by separating dependency installation stages
  - Simplified development Dockerfile for hot-reload functionality
  - Updated both Dockerfiles to use Node.js 20-alpine consistently

### Added
- **Docker Override Configuration**
  - Created `docker-compose.override.yml.example` for local customizations
  - Allows developers to override ports and settings without git conflicts
  - Provides examples for common local development scenarios

### Fixed
- **Configuration Issues**
  - Fixed NODE_ENV mismatch in development docker-compose
  - Corrected database initialization paths in production compose
  - Updated production-local.sh script to use new Docker setup
  - Fixed container naming inconsistencies across environments

### Security
- Removed hardcoded passwords from docker-compose.local-prod.yml
- Ensured all sensitive configurations use environment variables

---

## [2.11.0] - 2025-09-07

### Added
- **Comprehensive Docker Documentation**
  - Created DOCKER_GUIDE.md with complete setup instructions and architecture diagrams
  - Added Docker README with service details and troubleshooting guide
  - Enhanced docker-compose files with detailed inline comments
  - Added clear architecture overview showing PostgreSQL, Redis, Nginx, and App services

- **Package Management Improvements**
  - Updated Node.js from v18 to v20 in all Dockerfiles
  - Fixed husky prepare script for CI/CD environments
  - Added --ignore-scripts flag to prevent build errors in Docker

### Changed
- **Docker Infrastructure**
  - Updated Makefile to use modern `docker compose` instead of deprecated `docker-compose`
  - Improved docker-compose.production.yml with comprehensive service comments
  - Enhanced Dockerfile with Node.js 20 and proper npm script handling
  - Clarified container networking and volume persistence documentation

- **Documentation Organization**
  - Moved DOCKER_GUIDE.md to docs/ directory for better structure
  - Updated README.md with clear Docker deployment instructions
  - Added explicit service port mappings and network information

### Fixed
- **Security Issues**
  - Removed exposed Twilio credentials from git history
  - Cleaned repository with git filter-branch to remove sensitive data
  - Fixed .gitignore patterns to prevent future credential exposure
  - Force-pushed cleaned history to remote repository

- **Build Warnings**
  - Resolved all Node.js version compatibility warnings
  - Fixed npm EBADENGINE warnings for commander, lint-staged, listr2, nano-spawn
  - Corrected husky installation errors in Docker builds
  - Updated deprecated Docker Compose command syntax

### Security
- **Credential Management**
  - Identified and removed Twilio Account SID and Auth Token from repository
  - Implemented proper credential rotation procedures
  - Enhanced .gitignore to catch backup files with sensitive data
  - Documented security best practices for environment variables

---

## [2.10.0] - 2025-09-07

### Added
- **WhatsApp Testing Infrastructure**
  - Configured ngrok integration for local webhook testing
  - Added ngrok auth token to production environment configuration
  - Set up production simulation environment with Docker services
  - Created comprehensive WhatsApp sandbox testing workflow
  - Implemented local testing capability for Twilio WhatsApp Business API

### Changed
- **Environment Configuration**
  - Added `NGROK_AUTH_TOKEN` to `.env.production` for webhook tunneling
  - Updated production simulation script for better service management
  - Enhanced webhook endpoint configuration at `/webhook/whatsapp`

### Fixed
- **Security Credentials**
  - Identified and flagged exposed Twilio credentials in `.env` files
  - Recommended credential rotation for production security

### Documentation
- **Testing Setup Guide**
  - Added complete WhatsApp testing instructions
  - Documented ngrok setup and configuration process
  - Created step-by-step guide for Twilio sandbox integration
  - Updated testing procedures for local development

---

## [2.9.0] - 2025-09-07

### Removed
- **Complete FastSpring Payment Gateway Removal**
  - Deleted FastSpring controller (`/src/controllers/fastspringController.js` - 503 lines)
  - Removed FastSpring webhook endpoint (`/webhook/fastspring`)
  - Eliminated FastSpring test files and test cases
  - Removed FastSpring environment variables (`FASTSPRING_WEBHOOK_SECRET`)
  - Cleaned up FastSpring references from payment service
  - Updated OpenAPI specification to remove FastSpring endpoints and schemas
  - Removed FastSpring from all documentation files

### Changed
- **Database Schema Updates**
  - Renamed `fastspring_order_id` to `thrivecart_order_id` in `paid_users` table
  - Renamed `fastspring_subscription_id` to `thrivecart_subscription_id` in `paid_users` table
  - Renamed `fastspring_order_id` to `provider_order_id` in `payment_transactions` table
  - Renamed `fastspring_reference` to `provider_reference` in `payment_transactions` table
  - Updated default webhook source from `fastspring` to `thrivecart`

- **Payment System Consolidation**
  - Consolidated all payment processing to ThriveCart only
  - Simplified payment configuration and webhook handling
  - Updated integration tests to use ThriveCart exclusively
  - Modified test router to support only ThriveCart webhooks

- **Documentation Updates**
  - Updated README to reflect ThriveCart-only payment processing
  - Modified all API documentation to remove FastSpring references
  - Updated legal documents (Privacy Policy, Terms of Service)
  - Cleaned up configuration guides and deployment documentation

### Added
- **Database Migration Script**
  - Created `remove_fastspring.sql` migration for safe production data migration
  - Added column renaming migrations for existing databases
  - Included audit trail for migration completion

### Fixed
- Jest configuration regex error in `testPathIgnorePatterns`
- Test suite configuration for integration and e2e tests
- Payment controller column references in database queries

### Documentation
- **Comprehensive Documentation Update (September 7, 2025)**
  - Updated all documentation files to reflect v2.9.0 current state
  - Corrected version numbers, dates, and feature descriptions
  - Updated API documentation with current endpoints and rate limiting
  - Refreshed architecture documentation with current dependencies
  - Updated deployment guide with Makefile-based automation
  - Consolidated TODO and project status documents

---

## [2.8.0] - 2025-09-06

### Added
- **Production-Grade Docker Setup**
  - Multi-stage production Dockerfile with security hardening
  - Docker Compose production configuration with Nginx, Redis, PostgreSQL
  - Monitoring stack with Prometheus and Grafana
  - Automated backup and restore scripts for PostgreSQL
  - SSL/TLS support with self-signed certificate generator
  - Health checks for all services
  - Resource limits and reservations for containers

- **Deployment Automation**
  - Comprehensive Makefile with 30+ commands
  - Automated deployment workflows
  - Database migration management
  - Service scaling capabilities
  - Monitoring and health check commands

- **Monitoring Infrastructure**
  - Prometheus metrics collection
  - Grafana visualization dashboards
  - Node, PostgreSQL, and Redis exporters
  - Container monitoring with cAdvisor
  - Configurable alerting thresholds

### Changed
- **Project Structure Reorganization**
  - Moved all Docker configurations to `docker/` directory
  - Consolidated database files from `/database/`, `/db/` to single `src/db/`
  - Moved migrations to `src/db/migrations/`
  - Organized monitoring configs in `docker/monitoring/`
  - Restructured nginx configurations in `docker/nginx/`

- **Documentation Updates**
  - Merged and expanded deployment guide (597 lines)
  - Added complete Docker production setup instructions
  - Included step-by-step deployment procedures
  - Added troubleshooting section for common issues
  - Created Docker README explaining directory structure

### Fixed
- All failing tests now passing (100% success rate)
- Jest configuration syntax error with testPathIgnorePatterns
- Import paths updated for new database structure
- Docker Compose path references corrected

### Removed
- Unused `/memory/` directory (unimplemented orchestration system)
- Empty `/db/` directory
- Duplicate `/database/` directory
- Redundant deployment documentation in root

### Security
- Non-root user in production Docker containers
- Security headers in Nginx configuration
- Rate limiting on API endpoints
- SSL/TLS encryption enabled
- Firewall rules documentation

### Infrastructure
- Redis added for session management and caching
- Nginx reverse proxy with load balancing
- PostgreSQL with partitioning support
- Automated backup retention (30 days)
- Health endpoints for all services

---

## Version History Summary

| Version | Release Date | Key Features |
|---------|--------------|--------------|
| **2.13.0** | 2025-09-07 | Test suite improvements, fixed failing tests, added new test coverage, documented test roadmap |
| **2.12.0** | 2025-09-07 | Docker infrastructure optimization, consolidated configs, proper dev/prod separation |
| **2.11.0** | 2025-09-07 | Docker documentation, Node.js 20 upgrade, security fixes, credential cleanup |
| **2.10.0** | 2025-09-07 | WhatsApp testing infrastructure, ngrok integration, local webhook testing |
| **2.9.0** | 2025-09-07 | Complete FastSpring removal, payment system consolidation to ThriveCart only |
| **2.8.0** | 2025-09-06 | Production Docker setup, monitoring stack, deployment automation |
| **2.7.0** | 2025-09-06 | Phase 1 architecture completion, DatabaseSecurity full deployment, audit resolution |
| **2.6.0** | 2025-09-05 | Architecture improvements, enhanced security, optimized DB pooling |
| **2.5.0** | 2025-01-05 | 100% security audit resolution, enhanced sessions, SQL safety |