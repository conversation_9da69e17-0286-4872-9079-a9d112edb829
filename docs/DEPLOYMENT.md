# Deployment Guide

**Lock In - WhatsApp Habit Tracker Bot**
**Version**: 2.9.0
**Production Deployment Instructions**
**Last Updated**: September 7, 2025

---

## Table of Contents

- [Deployment Overview](#deployment-overview)
- [Quick Start - Docker Production](#quick-start---docker-production)
- [Local Development](#local-development)
- [Production Docker Deployment](#production-docker-deployment)
- [Cloud Provider Deployment](#cloud-provider-deployment)
- [Production Checklist](#production-checklist)
- [Common Operations](#common-operations)
- [Monitoring & Maintenance](#monitoring--maintenance)
- [Troubleshooting](#troubleshooting)

---

## Deployment Overview

### Deployment Options

| Method | Best For | Complexity | Cost |
|--------|----------|------------|------|
| **VPS with Docker** | Small-medium production | Medium | $5-40/month |
| **DigitalOcean Droplet** | Production, scalable | Medium | $5-20/month |
| **AWS/GCP/Azure** | Enterprise, high scale | High | Variable |
| **Heroku** | Quick deployment | Low | $7-25/month |

### Architecture Requirements

- **Node.js 18+** runtime environment
- **PostgreSQL 15+** database with partitioning
- **Redis 7+** for sessions and caching
- **Nginx** for reverse proxy and SSL
- **Docker 20.10+** and Docker Compose 2.0+
- **HTTPS** for webhook endpoints
- **Public IP** for Twilio webhooks
- **Email service** (SMTP provider)

---

## Quick Start - Docker Production

### Prerequisites

- Docker Engine 20.10+ and Docker Compose 2.0+
- 4GB RAM minimum (8GB recommended)
- 20GB free disk space
- Git

### Rapid Deployment

```bash
# 1. Clone and setup
git clone https://github.com/richvieren/lockin.git
cd lockin
make setup

# 2. Configure environment
cp .env.production.example .env.production
nano .env.production  # Edit with your values

# 3. Generate SSL certificate
make ssl-dev

# 4. Build and deploy
make build
make deploy-with-monitoring

# 5. Verify
make health-check
```

**Access Points:**
- Application: https://localhost
- Grafana: http://localhost:3001
- Prometheus: http://localhost:9090
- PgAdmin: http://localhost:5050

---

## Production Docker Deployment

### Step-by-Step Production Setup

#### Phase 1: Environment Configuration

1. **Initial Setup**
```bash
make setup
```

2. **Configure Production Environment**

Edit `.env.production`:

```env
# REQUIRED: Security
POSTGRES_PASSWORD=<strong-unique-password>
SESSION_SECRET=<random-64-character-string>

# REQUIRED: Twilio
TWILIO_ACCOUNT_SID=<your-sid>
TWILIO_AUTH_TOKEN=<your-auth-token>
TWILIO_PHONE_NUMBER=whatsapp:+**********

# REQUIRED: Payment Providers
THRIVECART_SECRET=<your-secret>

# REQUIRED: Email
EMAIL_FROM=<EMAIL>
EMAIL_USER=<smtp-username>
EMAIL_PASS=<smtp-password>

# Admin Tools
PGADMIN_PASSWORD=<strong-password>
GRAFANA_PASSWORD=<strong-password>
```

#### Phase 2: SSL Configuration

**Development (Self-Signed):**
```bash
make ssl-dev
```

**Production (Let's Encrypt):**
```bash
# Install certbot
sudo apt-get install certbot

# Generate certificate
sudo certbot certonly --standalone -d yourdomain.com

# Copy certificates
cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem docker/nginx/ssl/cert.pem
cp /etc/letsencrypt/live/yourdomain.com/privkey.pem docker/nginx/ssl/key.pem
```

#### Phase 3: Build and Deploy

```bash
# Build optimized images
make build

# Deploy with monitoring
make deploy-with-monitoring

# Verify deployment
make status
make health-check
```

### Docker Compose Services

The production setup includes:

**Core Services:**
- `nginx` - Reverse proxy with SSL, rate limiting
- `app` - Node.js application with health checks
- `postgres` - PostgreSQL 15 with partitioning
- `redis` - Session management and caching

**Monitoring Stack:**
- `prometheus` - Metrics collection
- `grafana` - Visualization dashboards
- `node-exporter` - System metrics
- `postgres-exporter` - Database metrics
- `redis-exporter` - Cache metrics

**Support Tools:**
- `pgadmin` - Database management UI
- `backup` - Automated backup service

---

## Cloud Provider Deployment

### DigitalOcean Droplet

```bash
# 1. Create Droplet (Ubuntu 22.04, 2GB RAM minimum)
doctl compute droplet create lockin-app \
  --image ubuntu-22-04-x64 \
  --size s-2vcpu-2gb \
  --region nyc1

# 2. SSH into droplet
ssh root@your-droplet-ip

# 3. Install Docker
curl -fsSL https://get.docker.com | sh

# 4. Clone and deploy
git clone https://github.com/richvieren/lockin.git
cd lockin
make setup
# Configure .env.production
make deploy-with-monitoring
```

### AWS EC2

```bash
# 1. Launch EC2 instance (t3.small minimum)
aws ec2 run-instances \
  --image-id ami-0c55b159cbfafe1f0 \
  --instance-type t3.small \
  --key-name your-key \
  --security-groups your-sg

# 2. Configure security group
# Open ports: 80, 443, 22

# 3. Deploy using Docker
# Follow Docker production steps above
```

### Heroku

```bash
# 1. Create Heroku app
heroku create your-app-name

# 2. Add PostgreSQL
heroku addons:create heroku-postgresql:hobby-dev

# 3. Add Redis
heroku addons:create heroku-redis:hobby-dev

# 4. Set environment variables
heroku config:set NODE_ENV=production
heroku config:set TWILIO_ACCOUNT_SID=your-sid
# Set all other required variables

# 5. Deploy
git push heroku main
```

---

## Production Checklist

### Pre-Deployment

- [ ] Generate strong passwords for all services
- [ ] Configure real SSL certificates (not self-signed)
- [ ] Set up domain name and DNS
- [ ] Configure firewall rules (ports 80, 443 only)
- [ ] Review and update rate limiting settings
- [ ] Test webhook endpoints with Twilio
- [ ] Configure email service (SMTP)
- [ ] Set up backup automation

### Security

- [ ] Enable firewall (ufw/iptables)
- [ ] Configure fail2ban for SSH
- [ ] Disable root SSH access
- [ ] Set up SSL certificate auto-renewal
- [ ] Enable HSTS headers
- [ ] Review Nginx security headers
- [ ] Implement IP whitelisting for admin routes
- [ ] Regular security updates schedule

### Monitoring

- [ ] Configure Grafana dashboards
- [ ] Set up alerting rules in Prometheus
- [ ] Configure log aggregation
- [ ] Set up uptime monitoring
- [ ] Configure backup verification
- [ ] Test disaster recovery procedure

---

## Common Operations

### Database Management

```bash
# Backup database
make backup

# Restore from backup
make restore

# Access PostgreSQL shell
make db-shell

# Run migrations
make migrate

# View database logs
make logs-db
```

### Application Management

```bash
# View application logs
make logs-app

# Restart services
make restart

# Scale application
make scale NUM=3

# Run tests
make test

# Access app shell
make app-shell
```

### Monitoring

```bash
# Check all health endpoints
make health-check

# Monitor resource usage
make monitor-cpu

# View all logs
make logs
```

---

## Monitoring & Maintenance

### Daily Tasks

- Monitor application logs for errors
- Check backup completion
- Review rate limiting metrics
- Monitor disk usage

### Weekly Tasks

- Review error trends in Grafana
- Check SSL certificate expiration
- Verify all health checks passing
- Review security logs

### Monthly Tasks

- Update Docker images
- Test restore procedure
- Rotate logs
- Security audit
- Performance review

### Automated Monitoring

**Set up cron jobs:**

```bash
# Edit crontab
crontab -e

# Add automated tasks
0 2 * * * cd /path/to/lockin && make backup
0 */6 * * * cd /path/to/lockin && make health-check
0 3 * * 0 docker system prune -af
```

**Configure Grafana Alerts:**

1. Access Grafana: http://localhost:3001
2. Import dashboards from `docker/monitoring/grafana/dashboards/`
3. Configure alert channels (email, Slack, etc.)
4. Set thresholds for:
   - CPU usage > 80%
   - Memory usage > 85%
   - Database connections > 90%
   - Response time > 1s

---

## Troubleshooting

### Service Won't Start

```bash
# Check logs
make logs

# Verify environment variables
cat .env.production

# Check port conflicts
netstat -tulpn | grep -E '(80|443|3000|5432)'

# Reset and rebuild
make clean
make build
make deploy
```

### Database Issues

```bash
# Check PostgreSQL status
docker ps | grep postgres
docker logs lockin-postgres

# Test connection
make db-shell

# Reset database
docker-compose -f docker-compose.production.yml down -v
make deploy
```

### SSL/HTTPS Issues

```bash
# Check certificates
ls -la docker/nginx/ssl/

# Verify Nginx config
docker logs lockin-nginx

# Test SSL
curl -v https://localhost

# Regenerate certificates
make ssl-dev
```

### Performance Issues

```bash
# Check resource usage
docker stats

# View metrics in Grafana
# http://localhost:3001

# Check Redis
make redis-cli
> INFO memory
> INFO stats

# Database performance
make db-shell
\> EXPLAIN ANALYZE <your-query>;
```

### Webhook Issues

```bash
# Test webhook endpoint
curl -X POST https://yourdomain.com/api/webhook/twilio \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "From=whatsapp:+**********&Body=test"

# Check webhook logs
docker logs lockin-app | grep webhook

# Verify Twilio signature
# Check TWILIO_AUTH_TOKEN is correct
```

---

## Backup & Recovery

### Automated Backups

```bash
# Manual backup
make backup

# Schedule automated backups
0 2 * * * cd /path/to/lockin && make backup
```

### Restore Procedure

```bash
# List available backups
ls -lh backups/

# Restore specific backup
make restore
# Enter filename when prompted

# Verify restoration
make db-shell
\> SELECT COUNT(*) FROM users;
```

### Disaster Recovery

1. **Backup Strategy:**
   - Daily automated backups
   - Keep 30 days of backups
   - Off-site backup storage

2. **Recovery Steps:**
   ```bash
   # 1. Provision new server
   # 2. Install Docker
   # 3. Clone repository
   git clone https://github.com/richvieren/lockin.git
   cd lockin
   
   # 4. Restore configuration
   scp backup-server:.env.production .
   
   # 5. Deploy services
   make deploy
   
   # 6. Restore database
   scp backup-server:backup_file.sql.gz backups/
   make restore
   ```

---

## Support

For deployment issues:

1. Check logs: `make logs`
2. Review this documentation
3. Check service health: `make health-check`
4. Review [TROUBLESHOOTING.md](./TROUBLESHOOTING.md)
5. Contact support with log excerpts

## Commands Reference

```bash
make help               # Show all available commands
make setup             # Initial setup
make deploy            # Deploy application
make deploy-with-monitoring  # Deploy with full monitoring
make stop              # Stop all services
make restart           # Restart services
make backup            # Create database backup
make restore           # Restore from backup
make logs              # View all logs
make health-check      # Check all health endpoints
make clean             # Remove all data (WARNING!)
```

---

## Local Testing with WhatsApp

For local testing with WhatsApp integration:

1. **Set up ngrok**:
   ```bash
   # Install ngrok
   brew install ngrok  # macOS
   snap install ngrok  # Linux
   
   # Configure auth token
   ngrok config add-authtoken YOUR_TOKEN
   
   # Start tunnel
   ngrok http 3000
   ```

2. **Configure Twilio Webhook**:
   - Use ngrok HTTPS URL: `https://YOUR_ID.ngrok-free.app/webhook/whatsapp`
   - Set in Twilio Console under WhatsApp Sandbox settings

3. **Test the Integration**:
   ```bash
   # Start production simulation
   ./scripts/production-local.sh start
   
   # Monitor logs
   ./scripts/production-local.sh logs
   
   # Check ngrok traffic
   open http://localhost:4040
   ```

See [WhatsApp Testing Guide](./WHATSAPP_TESTING_GUIDE.md) for detailed instructions.

---

**Last Updated**: September 7, 2025
**Version**: 2.11.0
**Deployment Status**: Production-ready with complete Docker infrastructure, monitoring stack, and WhatsApp testing support