# Local Production Testing Guide

## Overview

This guide covers setting up a production-like environment on your LOCAL machine for testing production configurations before actual deployment. This is NOT for real production servers - see [DEPLOYMENT.md](./DEPLOYMENT.md) for that.

## Table of Contents

1. [Production Environment Files](#production-environment-files)
2. [Local Production Testing](#local-production-testing)
3. [Database Configuration](#database-configuration)
4. [Twilio Setup](#twilio-setup)
5. [Security Configuration](#security-configuration)
6. [Performance Optimizations](#performance-optimizations)
7. [Monitoring & Logging](#monitoring--logging)
8. [Troubleshooting](#troubleshooting)

## Production Environment Files

### Core Files Created

- `.env.production` - Production environment variables
- `docker-compose.local-prod.yml` - Local production Docker setup
- `src/config/production.js` - Production configuration module
- `scripts/production-local.sh` - Production management script
- `src/middleware/productionOptimizations.js` - Performance & security middleware

### Environment Variables (.env.production)

```bash
# Core Configuration
NODE_ENV=production
PORT=3000
LOG_LEVEL=info

# Database - PostgreSQL
DATABASE_URL=postgresql://habituser:password@localhost:5432/habittracker_prod
DB_SSL=false  # Set to true for remote databases
DB_POOL_MIN=2
DB_POOL_MAX=10

# Twilio WhatsApp Configuration
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=+***********

# Redis Cache
REDIS_URL=redis://localhost:6379
CACHE_ENABLED=true
CACHE_TTL_SECONDS=3600

# Security
JWT_SECRET=production_jwt_secret_64_chars_minimum
ADMIN_PASSWORD=SuperSecureAdminPassword2024!
SESSION_SECRET=production_session_secret_64_chars

# GDPR Compliance
GDPR_ENABLED=true
DATA_RETENTION_DAYS=365
AUDIT_LOG_RETENTION_DAYS=730
PII_ENCRYPTION_ENABLED=true
```

## Local Production Testing

### Quick Start

```bash
# Start production environment
./scripts/production-local.sh start

# Check status
./scripts/production-local.sh status

# View logs
./scripts/production-local.sh logs

# Stop environment
./scripts/production-local.sh stop

# Restart services
./scripts/production-local.sh restart
```

### What Gets Started

1. **PostgreSQL** (Port 5432)
   - Production database with all migrations
   - Tables: users, habits, habit_logs, audit_logs, email_queue, data_exports, paid_users

2. **Redis** (Port 6379)
   - Session storage
   - Cache layer
   - Rate limiting data

3. **Node.js Application** (Port 3000)
   - Production mode with all optimizations
   - Security headers enabled
   - Rate limiting active
   - GDPR compliance features

## Database Configuration

### Production Schema

The production database includes all tables with proper indexes:

```sql
-- Core Tables
users              -- User accounts and settings
habits             -- User habits (max 5 per user)
habit_logs         -- Daily habit completion tracking
audit_logs         -- GDPR compliance audit trail
access_codes       -- Payment activation codes

-- New Production Tables
email_queue        -- Async email processing
data_exports       -- GDPR data export requests
paid_users         -- Subscription management
```

### Database Migrations

```bash
# Run migrations in production mode
NODE_ENV=production node src/db/migrate.js

# Apply additional production tables
docker exec -i habittracker-postgres-prod psql -U habituser -d habittracker_prod < src/db/migrations/add_missing_tables.sql
```

## Twilio Setup

### Configuration Steps

1. **Get Credentials**
   - Log into [Twilio Console](https://console.twilio.com)
   - Copy Account SID and Auth Token
   - Get WhatsApp-enabled phone number

2. **Configure Webhook**
   ```
   Webhook URL: https://your-domain.com/webhook/whatsapp
   Method: POST
   ```

3. **Test Connection**
   ```javascript
   // Verify Twilio setup
   const twilio = require('twilio');
   const client = twilio(accountSid, authToken);
   
   client.api.accounts(accountSid)
     .fetch()
     .then(account => console.log('Status:', account.status));
   ```

### WhatsApp Business API Setup

1. Join Twilio Sandbox (for testing)
2. Configure webhook URL in Twilio console
3. Test with actual WhatsApp messages

## Security Configuration

### Production Security Features

1. **Rate Limiting**
   - API: 100 requests per 15 minutes
   - Auth endpoints: 5 attempts per 15 minutes
   - Progressive penalties for violations

2. **Security Headers**
   ```javascript
   Content-Security-Policy
   X-Frame-Options: DENY
   X-Content-Type-Options: nosniff
   Strict-Transport-Security
   ```

3. **Database Security**
   - SQL injection prevention via DatabaseSecurity class
   - Parameterized queries only
   - Query validation and sanitization

4. **Authentication**
   - JWT tokens with 7-day expiration
   - HMAC-SHA256 webhook verification
   - Admin panel protection

## Performance Optimizations

### Enabled in Production

1. **Compression**
   - GZIP compression for responses > 1KB
   - Level 6 compression (balanced)

2. **Connection Pooling**
   - PostgreSQL: 10 connections (min: 2)
   - Redis: Connection reuse
   - Automatic retry logic

3. **Caching**
   - Redis cache with 1-hour TTL
   - Static asset caching (1 year)
   - Database query result caching

4. **Response Optimizations**
   - Remove unnecessary headers
   - Cache-Control headers
   - ETags for static content

## Monitoring & Logging

### Log Configuration

```javascript
// Production logging
LOG_DIR=./logs/production
LOG_MAX_SIZE=10m
LOG_MAX_FILES=14d
LOG_COMPRESS=true
AUDIT_LOG_ENABLED=true
```

### Health Checks

```bash
# Check application health
curl http://localhost:3000/health

# Response:
{
  "status": "healthy",
  "timestamp": "2025-09-07T01:48:41.141Z",
  "uptime": 10.376
}
```

### Performance Monitoring

- Request timing (warns if > 1 second)
- Memory usage monitoring
- Slow query detection
- Error rate tracking

## Troubleshooting

### Common Issues & Solutions

#### Database Connection Errors

```bash
# Error: The server does not support SSL connections
# Solution: Set DB_SSL=false in .env.production for local PostgreSQL
```

#### Missing Tables

```bash
# Apply missing tables migration
cat src/db/migrations/add_missing_tables.sql | docker exec -i habittracker-postgres-prod psql -U habituser -d habittracker_prod
```

#### Email Verification Failures

```bash
# For testing, use Ethereal Email or disable verification
SMTP_HOST=smtp.ethereal.email
SMTP_PORT=587
```

#### Port Already in Use

```bash
# Find and kill process on port 3000
lsof -i :3000
kill -9 <PID>
```

### Checking Logs

```bash
# Application logs
tail -f logs/production/*.log

# Docker container logs
docker logs habittracker-postgres-prod
docker logs habittracker-redis-prod

# Background process logs
./scripts/production-local.sh logs
```

## Production Deployment Checklist

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Redis cache enabled
- [ ] Twilio credentials verified
- [ ] Email service configured
- [ ] SSL certificates installed
- [ ] Rate limiting enabled
- [ ] Security headers active
- [ ] Monitoring configured
- [ ] Backup strategy implemented
- [ ] Health checks passing
- [ ] Load testing completed

## Security Best Practices

1. **Never commit credentials** - Use environment variables
2. **Rotate secrets regularly** - Especially JWT and API keys
3. **Enable SSL/TLS** - For all production traffic
4. **Implement rate limiting** - Prevent abuse
5. **Log security events** - Track authentication and access
6. **Regular updates** - Keep dependencies current
7. **Database backups** - Automated daily backups
8. **Access control** - Limit production access

## Support

For issues or questions about production setup:
1. Check logs for detailed error messages
2. Review this documentation
3. Consult the troubleshooting guide
4. Contact the development team