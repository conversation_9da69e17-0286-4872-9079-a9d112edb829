# Comprehensive Test Suite for Lock In Habit Tracker

**Version**: 2.13.0
**Last Updated**: September 7, 2025

## Overview

This test suite provides comprehensive coverage for the Lock In habit tracking application, including:

- **Unit Tests**: Individual components (models, utilities, services, controllers, middleware)
- **Integration Tests**: Core business flows across multiple modules
- **End-to-End Tests**: Full payment and webhook processing flows
- **Coverage**: 25.45% statements, 18.98% branches, 31.32% functions, 25.35% lines
- **Test Framework**: Jest with Supertest for HTTP testing
- **Target Coverage**: 80% minimum for all metrics

## Test Structure

### Unit Tests

#### Models (`tests/models/`)

- **User.test.js**: CRUD operations, state updates, authentication flows
- **Habit.test.js**: Habit management and analytics calculations
- **AuditLog.test.js**: Audit logging and cleanup

#### Utilities (`tests/unit/utils/`)

- **codeGenerator.test.js**: Access/affiliate code generation and validation
- **piiHasher.test.js**: PII sanitization and hashing for compliance
- **motivationalQuotes.test.js**: Quote selection by category/context
- **sqlIntervalBuilder.test.js**: Safe SQL interval construction
- **databaseSecurity.test.js**: Query building and SQL injection prevention (existing)

#### Services (`tests/services/`)

- **emailService.test.js**: Email queue processing and template delegation (FIXED - timeout issues resolved)
- **subscriptionService.test.js**: Subscription status, expiration, renewal
- **complianceService.test.js**: Age verification, consent flows, GDPR compliance
- **sessionManager.test.js**: Session management, expiration, rate limiting (NEW)
- **paymentService.test.js**: Payment processing, access codes, ThriveCart integration (NEW)
- **handlers/menuHandlers.test.js**: Menu navigation and user interaction flows (NEW)

#### Controllers (`tests/controllers/`)

- **thrivecartController.test.js**: ThriveCart webhook handling and verification
- **webhookControllerCompliant.test.js**: WhatsApp webhook message processing (NEW)

#### Middleware (`tests/middleware/`)

- **compliance.test.js**: STOP/START keyword handling, opt-out compliance
- **enhancedValidation.test.js**: Input sanitization, Joi validation, XSS/SQL protection

### Integration Tests (`tests/integration/`)

- **webhookFlow.test.js**: End-to-end webhook message processing

### End-to-End Tests (`tests/e2e/`)

- **fullPaymentFlow.test.js**: Complete ThriveCart payment processing to email delivery

## Running Tests

### All Tests

```bash
npm test
```

### Specific Test Types

```bash
# Run unit tests only
npm test -- --testPathPattern=unit

# Run integration tests
npm test -- --testPathPattern=integration

# Run e2e tests
npm test -- --testPathPattern=e2e

# Run tests for specific file
npm test -- tests/services/subscriptionService.test.js
```

### Coverage Reports

```bash
# Generate coverage report
npm test -- --coverage

# View HTML coverage report
npm test -- --coverage --coverageReporters=html
open coverage/lcov-report/index.html
```

### Test Configuration

See `jest.config.js` for configuration details:

- **Test Environment**: Node.js
- **Coverage Threshold**: 80% minimum for branches, functions, lines, statements
- **Test Timeout**: 10 seconds per test
- **Setup**: Global setup in `tests/setup.js`
- **Test Matching**: All `*.test.js` files in `tests/` directory

## Test Best Practices

### Mocking Strategy

- Database interactions mocked using `jest.mock('../db/connection')`
- External services (email, SMS) mocked to prevent actual sending
- Crypto operations mocked for predictable testing
- Use `jest.useFakeTimers()` for time-sensitive tests
- Mock dates with `jest.setSystemTime()` for consistency

### Testing Philosophy

- **Unit Isolation**: Each component tested in isolation with mocked dependencies
- **Edge Cases**: Invalid inputs, error conditions, boundary values
- **Async Handling**: Proper async/await and promise rejection testing
- **Security**: Test validation, sanitization, and error handling for security features
- **Compliance**: Verify GDPR/CCPA compliance flows and data protection

### Database Testing

For integration tests that require database state:

- Use test database (configured in `src/db/connection.js`)
- Clear test data between tests using setup/teardown
- Mock production database to prevent accidental writes

### Environment Variables

Test environment uses:

```bash
NODE_ENV=test
JWT_SECRET=test-jwt-secret
THRIVECART_WEBHOOK_SECRET=test-secret
PAYMENT_TEST_MODE=true
```

## Code Coverage Report

Current coverage metrics (as of v2.13.0):

- **Statements**: 25.45% (Target: 80%)
- **Branches**: 18.98% (Target: 80%)
- **Functions**: 31.32% (Target: 80%)
- **Lines**: 25.35% (Target: 80%)

### Test Suite Status
- **Total Tests**: 379 (210 passing, 169 failing)
- **Test Suites**: 22 (9 passing, 13 failing)
- **Pass Rate**: 55.4%

Coverage reports generated in `coverage/` directory.

### Recent Test Improvements
- Fixed `sqlIntervalBuilder.test.js` - Resolved 3 failing unit validation tests
- Fixed `emailService.test.js` - Resolved timeout errors with async handling
- Fixed `User.test.js` - Updated 10 tests for API changes and removed non-existent methods
- Added comprehensive test suites for critical 0% coverage components

## Future Improvements

### Priority 1: Achieve 80% Coverage Target
- **Critical Components Needing Tests (0% coverage)**:
  - All handler services (habitHandlers, onboardingHandlers, statsHandlers)
  - Middleware (rateLimiter, productionOptimizations)
  - Data services (dataRetentionService, complianceAuditService, userRightsService)
  - Routes and app.js main application file

- **Components Needing Coverage Improvement**:
  - Habit model (54.63% → 80%+)
  - CoreStateMachineStrategy (21.55% → 80%+)
  - Payment webhook handlers (3.37% → 80%+)
  
### Priority 2: Fix Failing Tests
- Resolve 169 failing tests (mostly integration tests)
- Fix database mocking in integration tests
- Update middleware tests with proper request/response mocking

### Priority 3: Additional Testing
- **More Integration Tests**: Test complete user journeys (onboarding to habit tracking)
- **Performance Tests**: Load testing for webhook endpoints
- **Security Tests**: Vulnerability scanning and fuzz testing
- **E2E Tests**: Complete flows from WhatsApp message to habit tracking
- **API Contract Tests**: Ensure webhook payloads match expected schemas

## Troubleshooting

### Tests Failing

1. Check if required mocks are present
2. Verify environment variables are set
3. Ensure test database is accessible
4. Review error logs in test output

### Coverage Low

1. Add missing test cases for uncovered branches
2. Test error paths and edge cases
3. Ensure all public methods are exercised

### Running Specific Tests

Use Jest's powerful filtering:

```bash
# Test specific service
npm test -- --testPathPattern=subscriptionService

# Test specific category
npm test -- --testPathPattern=controllers

# Test with verbose output
npm test -- --verbose
```

## Contribution Guidelines

When adding new features:

1. **Write tests first** (TDD approach recommended)
2. **Aim for 80%+ coverage** on new code
3. **Test error conditions** and edge cases
4. **Mock external dependencies** to isolate unit tests
5. **Use descriptive test names** following Jest conventions
6. **Test security features** especially for payment and webhook handling
7. **Include integration tests** for cross-service functionality

## Test Configuration

### Jest Configuration
- **Config File**: `jest.config.js`
- **Coverage Directory**: `coverage/`
- **Test Environment**: Node.js
- **Setup Files**: `tests/setup.js`

### Test Database
- Uses separate test database for isolation
- Automatic cleanup between test runs
- Transaction rollback for data integrity

---

**Testing Framework**: Jest 29.7.0
**HTTP Testing**: Supertest 6.3.3
**Coverage Target**: 80%+ for all new code
**Last Updated**: September 7, 2025