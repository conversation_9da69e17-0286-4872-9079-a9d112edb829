# API Documentation

**Lock In - WhatsApp Habit Tracker Bot**
**Version**: 2.9.0
**Base URL**: `https://your-domain.com`

---

## Table of Contents

- [Authentication](#authentication)
- [Public Endpoints](#public-endpoints)
- [Webhook Endpoints](#webhook-endpoints)
- [Admin Endpoints](#admin-endpoints)
- [Test Endpoints](#test-endpoints)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)

---

## Authentication

### Admin Authentication

Admin endpoints require JWT authentication. Obtain a token by logging in:

```http
POST /admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "your_admin_password"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": "24h"
}
```

**Usage:**
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

---

## Public Endpoints

### Health Check

Check application health and status.

```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-28T10:30:00.000Z",
  "uptime": 3600,
  "database": "connected",
  "services": {
    "twilio": "configured",
    "email": "ready",
    "payments": "active"
  }
}
```

### Legal Documents

Access privacy policy and terms of service.

```http
GET /privacy
GET /terms
```

**Response:** Redirects to static HTML files in `/legal/` directory.

---

## Webhook Endpoints

### WhatsApp Webhook

Receives incoming WhatsApp messages from Twilio.

```http
POST /webhook/whatsapp
Content-Type: application/x-www-form-urlencoded
X-Twilio-Signature: signature_hash

Body=Hello&From=%2B1234567890&To=%2B0987654321&MessageSid=SM...
```

**Parameters:**
- `Body` (string): Message content
- `From` (string): Sender's phone number (E.164 format)
- `To` (string): Recipient's phone number
- `MessageSid` (string): Twilio message identifier

**Response:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Message>Welcome! Please enter your access code...</Message>
</Response>
```

### ThriveCart Webhook

Handles ThriveCart payment events.

```http
POST /webhook/thrivecart
Content-Type: application/x-www-form-urlencoded

event=order.success&customer_email=<EMAIL>&product_name=Monthly+Subscription...
```

**Verification:**
```http
HEAD /webhook/thrivecart
```

**Supported Events:**
- `order.success`
- `order.refund`
- `subscription.payment`
- `subscription.cancelled`
- `subscription.resumed`

---

## Admin Endpoints

> **Note:** All admin endpoints require JWT authentication and are only available when `ADMIN_ACCESS=true` or in development mode.

### Compliance Statistics

Get comprehensive compliance and audit statistics.

```http
GET /admin/compliance/stats
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "audit": {
    "totalEvents": 1250,
    "eventsByType": {
      "USER_CREATED": 45,
      "HABIT_LOGGED": 890,
      "MESSAGE_RECEIVED": 315
    },
    "last24Hours": 127
  },
  "retention": {
    "usersToDelete": 3,
    "dataMinimized": 15,
    "lastCleanup": "2025-01-27T10:00:00.000Z"
  },
  "recommendations": [
    {
      "type": "data_minimization",
      "description": "5 users have inactive sessions > 30 days",
      "action": "Consider data cleanup"
    }
  ],
  "timestamp": "2025-01-28T10:30:00.000Z"
}
```

### Compliance Report

Generate detailed compliance report.

```http
GET /admin/compliance/report
Authorization: Bearer <jwt_token>
```

### Data Cleanup

Trigger manual data cleanup process.

```http
POST /admin/compliance/cleanup
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "dryRun": true,
  "olderThanDays": 90
}
```

### Retention Check

Check data retention compliance status.

```http
GET /admin/compliance/check
Authorization: Bearer <jwt_token>
```

---

## Test Endpoints

> **Note:** Test endpoints are only available when `PAYMENT_TEST_MODE=true`.

### Test Webhook Simulator

Simulate ThriveCart webhook events for testing.

```http
POST /test/webhook
Content-Type: application/json

{
  "eventType": "order.completed",
  "email": "<EMAIL>",
  "subscriptionType": "monthly"
}
```

### Test ThriveCart Webhook

Simulate ThriveCart webhook events.

```http
POST /test/webhook/thrivecart
Content-Type: application/json

{
  "event": "order.success",
  "customer_email": "<EMAIL>",
  "product_name": "Monthly Subscription"
}
```

### Test Status

Get test environment status and statistics.

```http
GET /test/status
```

**Response:**
```json
{
  "testMode": true,
  "stats": {
    "paid_users": 5,
    "access_codes": 10,
    "used_codes": 3,
    "pending_emails": 2
  },
  "timestamp": "2025-01-28T10:30:00.000Z"
}
```

---

## Error Handling

### Standard Error Response

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2025-01-28T10:30:00.000Z",
  "details": {
    "field": "Additional error details"
  }
}
```

### HTTP Status Codes

| Code | Description |
|------|-------------|
| `200` | Success |
| `400` | Bad Request - Invalid input |
| `401` | Unauthorized - Missing or invalid authentication |
| `403` | Forbidden - Insufficient permissions |
| `404` | Not Found - Resource doesn't exist |
| `429` | Too Many Requests - Rate limit exceeded |
| `500` | Internal Server Error - Server-side error |

### Common Error Codes

- `INVALID_ACCESS_CODE` - Access code not found or expired
- `PAYMENT_REQUIRED` - Valid subscription required
- `RATE_LIMITED` - Too many requests
- `VALIDATION_ERROR` - Input validation failed
- `WEBHOOK_SIGNATURE_INVALID` - Webhook signature verification failed

---

## Rate Limiting

### Global Rate Limit
- **Limit**: 20 requests per 5 minutes
- **Scope**: Per IP address
- **Progressive Penalties**: Enabled with exponential backoff
- **Headers**:
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset timestamp

### Per-User Rate Limit
- **Limit**: 20 requests per 5 minutes
- **Scope**: Per phone number (for WhatsApp webhook)
- **Response**: HTTP 429 with retry-after header
- **Progressive Delay**: Increases with repeated violations

### Rate Limit Response

```json
{
  "error": "Too many requests. Please try again later.",
  "retryAfter": 900
}
```

---

## Webhook Security

### Signature Verification

All webhooks implement signature verification:

1. **Twilio**: Uses `X-Twilio-Signature` header with HMAC-SHA1
2. **ThriveCart**: Uses custom signature verification
3. **ThriveCart**: Uses custom signature verification

### Best Practices

- Always verify webhook signatures in production
- Use HTTPS endpoints for webhook URLs
- Implement idempotency for webhook processing
- Log webhook events for debugging and compliance

---

## SDK Examples

### Node.js Example

```javascript
const axios = require('axios');

// Get health status
const health = await axios.get('https://your-domain.com/health');
console.log(health.data);

// Admin login
const login = await axios.post('https://your-domain.com/admin/login', {
  password: 'your_admin_password'
});

const token = login.data.token;

// Get compliance stats
const stats = await axios.get('https://your-domain.com/admin/compliance/stats', {
  headers: { Authorization: `Bearer ${token}` }
});
```

### cURL Examples

```bash
# Health check
curl https://your-domain.com/health

# Admin login
curl -X POST https://your-domain.com/admin/login \
  -H "Content-Type: application/json" \
  -d '{"password":"your_admin_password"}'

# Get compliance stats
curl https://your-domain.com/admin/compliance/stats \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

## Changelog

### Version 2.9.0 (Current)
- Complete FastSpring removal, ThriveCart-only payment processing
- Enhanced rate limiting with progressive penalties (20 req/5min)
- HMAC-SHA256 signature verification for all webhooks
- DatabaseSecurity wrapper for SQL injection prevention
- Enhanced admin endpoints with compliance reporting

### Version 1.0.0
- Initial API documentation
- JWT authentication for admin endpoints
- Comprehensive webhook support
- Rate limiting implementation
- GDPR compliance endpoints

---

**Last Updated**: September 7, 2025
**API Version**: 2.9.0
