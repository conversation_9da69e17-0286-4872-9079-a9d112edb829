# Changelog

All notable changes to the **Lock In - WhatsApp Habit Tracker Bot** project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

# Previous Changelogs: `docs/previous_changelogs/`

---

## [2.14.0] - 2025-09-07

### Added
- Comprehensive WhatsApp testing documentation and setup guide
- Database schema migration fixes for compliance fields
- Missing columns for GDPR compliance tracking (consent_given, terms_accepted, age_verified)
- Audit log enhancements with proper field structure

### Changed
- Updated webhook endpoint from `/webhook/twilio` to `/webhook/whatsapp` for clarity
- Temporarily disabled Twilio signature validation for ngrok local testing
- Cleaned up misleading webhook URL environment variables

### Fixed
- Database migration issues preventing user creation
- Missing database columns for compliance features
- Audit logging failures due to missing fields
- Email queue processing errors with missing retry_count and priority columns
- WhatsApp webhook integration with proper ngrok tunneling

### Testing
- Successfully tested end-to-end WhatsApp message flow
- Verified state machine operation with main menu responses
- Confirmed database user creation and session management

### Security
- Identified Twilio signature validation issue with ngrok (requires re-enabling for production)
- Added notes about webhook URL configuration in external services

---

## Version History Summary

See Previous Changelogs for More Details: `docs/previous_changelogs/`


| Version | Release Date | Key Features |
|---------|--------------|--------------|
| **2.14.0** | 2025-09-07 | WhatsApp testing setup, database migrations fix, webhook configuration cleanup |
| **2.13.0** | 2025-09-07 | Test suite improvements, fixed failing tests, added new test coverage, documented test roadmap |
| **2.12.0** | 2025-09-07 | Docker infrastructure optimization, consolidated configs, proper dev/prod separation |

