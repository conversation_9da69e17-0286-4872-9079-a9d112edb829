# Twilio WhatsApp Integration Guide

## Overview

This guide covers the complete setup process for integrating Twilio's WhatsApp Business API with the Lock In Habit Tracker bot.

## Prerequisites

- Twilio account (free trial or paid)
- WhatsApp Business API access (or Sandbox for testing)
- Phone number capable of receiving SMS for verification
- Credit card for Twilio billing (if using paid features)

## Table of Contents

1. [Twilio Account Setup](#twilio-account-setup)
2. [Getting Your Credentials](#getting-your-credentials)
3. [WhatsApp Sandbox Setup](#whatsapp-sandbox-setup)
4. [Production WhatsApp Setup](#production-whatsapp-setup)
5. [Webhook Configuration](#webhook-configuration)
6. [Testing Your Integration](#testing-your-integration)
7. [Security Best Practices](#security-best-practices)
8. [Troubleshooting](#troubleshooting)

## Twilio Account Setup

### Step 1: Create Twilio Account

1. Visit [Twilio Sign Up](https://www.twilio.com/try-twilio)
2. Complete registration with:
   - Email address
   - Phone number for verification
   - Strong password

### Step 2: Verify Your Account

1. Enter the verification code sent to your phone
2. Complete the onboarding questionnaire
3. Note your trial balance ($15 free credit typically)

## Getting Your Credentials

### Required Credentials

Navigate to [Twilio Console](https://console.twilio.com) to find:

1. **Account SID**: `ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` (example)
   - Found on Console Dashboard
   - Format: AC followed by 32 characters

2. **Auth Token**: Keep this secret!
   - Click "View" on Console Dashboard
   - Never commit to version control

3. **Phone Number**: WhatsApp-enabled number
   - Format: `+**********`
   - Must be WhatsApp Business API enabled

### Storing Credentials

Add to your `.env.production` file:

```bash
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=+***********
```

## WhatsApp Sandbox Setup

### For Development/Testing

1. **Access Sandbox**
   - Go to: Console → Messaging → Try it out → Send a WhatsApp message
   - Or direct: [WhatsApp Sandbox](https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-quickstart)

2. **Join Sandbox**
   - Send the join code (e.g., "join word-word") to the Twilio WhatsApp number
   - You'll receive confirmation: "Twilio Sandbox: ✅ You are all set!"

3. **Configure Sandbox Webhook**
   ```
   When a message comes in:
   URL: https://your-ngrok-url.ngrok.io/webhook/whatsapp
   Method: POST
   ```

### Using ngrok for Local Development

```bash
# Install ngrok
npm install -g ngrok

# Start your local server
NODE_ENV=production node src/server.js

# In another terminal, expose port 3000
ngrok http 3000

# Copy the HTTPS URL (e.g., https://abc123.ngrok.io)
```

## Production WhatsApp Setup

### Step 1: Request WhatsApp Business API Access

1. **Apply for Access**
   - Go to: Console → Messaging → WhatsApp → Get Started
   - Complete Business Profile
   - Submit for Facebook Business Verification

2. **Requirements**
   - Business website
   - Business email (not personal)
   - Legal business entity
   - Privacy policy and terms of service

### Step 2: Configure Production Number

1. **Purchase Twilio Phone Number**
   ```
   Console → Phone Numbers → Buy a Number
   - Select country
   - Enable: SMS, MMS, Voice
   - Monthly cost: ~$1-15 depending on country
   ```

2. **Enable WhatsApp on Number**
   ```
   Console → Messaging → WhatsApp → Senders
   - Add your phone number
   - Complete WhatsApp Business Profile
   - Wait for approval (24-48 hours)
   ```

## Webhook Configuration

### Production Webhook URL

Set in Twilio Console:
```
https://your-domain.com/webhook/whatsapp
```

### Webhook Security

The application validates webhook signatures:

```javascript
// Automatic validation in webhookController.js
verifyWebhook(req, res, next) {
  const twilioSignature = req.headers['x-twilio-signature'];
  const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
  
  const isValid = twilio.validateRequest(
    process.env.TWILIO_AUTH_TOKEN,
    twilioSignature,
    url,
    req.body
  );
  
  if (!isValid) {
    return res.status(403).send('Forbidden');
  }
  next();
}
```

## Testing Your Integration

### Test Checklist

1. **Verify Credentials**
   ```javascript
   // Test script
   const twilio = require('twilio');
   const client = twilio(
     'ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
     'your_auth_token'
   );
   
   client.api.accounts('ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
     .fetch()
     .then(account => {
       console.log('✅ Account Status:', account.status);
       console.log('✅ Account Type:', account.type);
     })
     .catch(err => console.error('❌ Error:', err.message));
   ```

2. **Send Test Message**
   ```javascript
   client.messages.create({
     from: 'whatsapp:+***********', // Sandbox number
     to: 'whatsapp:+**********',     // Your WhatsApp
     body: 'Test message from Lock In Bot'
   });
   ```

3. **Test Webhook Reception**
   - Send message to bot number
   - Check server logs for incoming webhook
   - Verify response is sent back

### Expected Flow

1. User sends: "Hi" to WhatsApp number
2. Webhook receives at `/webhook/whatsapp`
3. Bot processes through state machine
4. Response sent back via Twilio API
5. User sees response in WhatsApp

## Security Best Practices

### Credential Management

1. **Never commit credentials**
   ```bash
   # Add to .gitignore
   .env
   .env.production
   .env.local
   ```

2. **Use environment variables**
   ```javascript
   // Good
   const accountSid = process.env.TWILIO_ACCOUNT_SID;
   
   // Bad
   const accountSid = 'ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx';
   ```

3. **Rotate tokens regularly**
   - Change Auth Token monthly
   - Update webhook URLs if compromised

### Webhook Security

1. **Always validate signatures**
2. **Use HTTPS only**
3. **Implement rate limiting**
4. **Log suspicious activity**

## Troubleshooting

### Common Issues

#### "Invalid Twilio Signature"

**Cause**: Webhook validation failing
**Solution**:
- Verify Auth Token is correct
- Check webhook URL matches exactly
- Ensure HTTPS is used
- Verify no proxy is modifying headers

#### "Message not delivered"

**Cause**: Number not WhatsApp enabled
**Solution**:
- Check number is verified for WhatsApp
- Ensure user has joined sandbox (if testing)
- Verify message format is correct

#### "Account not active"

**Cause**: Trial expired or suspended
**Solution**:
- Add payment method
- Check account status in console
- Contact Twilio support

### Debug Commands

```bash
# Check Twilio connection
curl -X GET "https://api.twilio.com/2010-04-01/Accounts/YOUR_ACCOUNT_SID.json" \
  -u YOUR_ACCOUNT_SID:YOUR_AUTH_TOKEN

# Test webhook locally
curl -X POST http://localhost:3000/webhook/whatsapp \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "From=whatsapp:%2B**********&Body=Test"

# View Twilio logs
# Go to: Console → Monitor → Logs → Errors
```

## Rate Limits & Pricing

### WhatsApp Business API Limits

- **Session Messages**: 1,000/day during trial
- **Template Messages**: Requires approval
- **Media Messages**: 5MB max size

### Pricing (Approximate)

- **Phone Number**: $1-15/month
- **WhatsApp Messages**: $0.005-0.02 per message
- **Session Duration**: 24 hours per conversation

## Additional Resources

- [Twilio WhatsApp API Docs](https://www.twilio.com/docs/whatsapp)
- [Webhook Security Guide](https://www.twilio.com/docs/usage/webhooks/webhooks-security)
- [WhatsApp Business API Policies](https://www.whatsapp.com/legal/business-policy)
- [Twilio Status Page](https://status.twilio.com/)

## Support

For Twilio-specific issues:
- [Twilio Support Center](https://support.twilio.com)
- [Community Forums](https://www.twilio.com/community)
- Email: <EMAIL>

For Lock In Bot issues:
- Check application logs
- Review this documentation
- Contact development team