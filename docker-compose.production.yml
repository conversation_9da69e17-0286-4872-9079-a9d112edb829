# Production Docker Compose Configuration
# This file orchestrates the complete production stack for Lock In Habit Tracker
# Services: Nginx (reverse proxy), Node.js App, PostgreSQL (database), Redis (cache)
# 
# Usage: docker compose -f docker-compose.production.yml up -d
# Prerequisites: .env file with required variables

version: '3.8'

services:
  # Nginx Reverse Proxy - Handles SSL termination, load balancing, and static assets
  nginx:
    image: nginx:alpine
    container_name: lockin-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - nginx-cache:/var/cache/nginx
    networks:
      - app-network
    depends_on:
      - app
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Node.js Application - Main WhatsApp bot service
  # Connects to PostgreSQL for data storage and Redis for session management
  app:
    build:
      context: .
      dockerfile: docker/app/Dockerfile
    container_name: lockin-app
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_URL: postgresql://${POSTGRES_USER:-lockin}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-lockin_prod}
      REDIS_URL: redis://redis:6379
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_PHONE_NUMBER: ${TWILIO_PHONE_NUMBER}
      THRIVECART_SECRET: ${THRIVECART_SECRET}
      EMAIL_FROM: ${EMAIL_FROM}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_DIR: /app/logs
      SESSION_SECRET: ${SESSION_SECRET}
      RATE_LIMIT_WINDOW_MS: ${RATE_LIMIT_WINDOW_MS:-900000}
      RATE_LIMIT_MAX_REQUESTS: ${RATE_LIMIT_MAX_REQUESTS:-100}
    volumes:
      - app-logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - app-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 256M

  # PostgreSQL Database - Primary data storage for users, habits, and logs
  # Data persists in postgres-data volume
  postgres:
    image: postgres:15-alpine
    container_name: lockin-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-lockin_prod}
      POSTGRES_USER: ${POSTGRES_USER:-lockin}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=en_US.utf8 --lc-ctype=en_US.utf8"
      POSTGRES_HOST_AUTH_METHOD: scram-sha-256
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./src/db/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/02-init.sql:ro
      - ./backups:/backups
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-lockin} -d ${POSTGRES_DB:-lockin_prod}"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M

  # Redis Cache - Session storage and caching layer
  # Used for rate limiting, session management, and temporary data
  redis:
    image: redis:7-alpine
    container_name: lockin-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M

  # PgAdmin - Web-based PostgreSQL administration (optional, remove in production)
  # Access at http://localhost:5050
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: lockin-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD}
      PGADMIN_CONFIG_SERVER_MODE: 'True'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'True'
    ports:
      - "5050:80"
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    networks:
      - app-network
    depends_on:
      - postgres
    profiles:
      - tools

  backup:
    image: postgres:15-alpine
    container_name: lockin-backup
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-lockin_db}
      POSTGRES_USER: ${POSTGRES_USER:-lockin}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    networks:
      - app-network
    depends_on:
      - postgres
    profiles:
      - backup
    command: /bin/sh -c "chmod +x /backup.sh && /backup.sh"

# Docker Volumes - Persistent data storage
# These volumes ensure data persists across container restarts
volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  app-logs:
    driver: local
  nginx-cache:
    driver: local
  pgadmin-data:
    driver: local

# Docker Networks - Internal communication between containers
# All services communicate through this network using container names as hostnames
networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16