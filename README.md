# Lock In - WhatsApp Habit Tracker Bot

<div align="center">

![Lock In Logo](https://img.shields.io/badge/Lock%20In-Habit%20Tracker-blue?style=for-the-badge)
[![Node.js](https://img.shields.io/badge/Node.js-20+-green?style=flat-square&logo=node.js)](https://nodejs.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue?style=flat-square&logo=postgresql)](https://postgresql.org/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue?style=flat-square&logo=docker)](https://docker.com/)
[![Version](https://img.shields.io/badge/Version-2.11.0-blue?style=flat-square)]()
[![Status](https://img.shields.io/badge/Status-Proprietary-red?style=flat-square)]()

**Production-ready WhatsApp habit tracking bot with ThriveCart payment integration, GDPR compliance, and enterprise-grade security.**

[Features](#features) • [Quick Start](#quick-start) • [Documentation](#documentation) • [Deployment](#deployment)

</div>

---

## 🚀 Features

### Core Functionality
- ✅ **WhatsApp Integration**: Native WhatsApp Business API via Twilio
- ✅ **Habit Tracking**: Track up to 5 daily habits with visual progress
- ✅ **Smart State Management**: LOCKED → ONBOARDING → ACTIVE user progression
- ✅ **Progress Analytics**: 7-day, 30-day, and 100-day progress reports
- ✅ **Streak Tracking**: Current streak calculation and motivation

### Payment & Subscription
- ✅ **ThriveCart Integration**: Secure payment processing with HMAC-SHA256 verification
- ✅ **Multiple Tiers**: Weekly ($2.99), Monthly ($5.99), Yearly ($39.99), Lifetime ($99)
- ✅ **Access Code System**: Secure account activation via email (format: START HABIT-XXXXX)
- ✅ **Subscription Management**: Automated renewal and status tracking
- ✅ **Email Automation**: Purchase confirmations and access codes via Brevo SMTP

### Security & Compliance
- ✅ **GDPR Compliant**: Data retention, user rights, comprehensive audit logging
- ✅ **Webhook Security**: HMAC-SHA256 signature verification (Twilio, ThriveCart)
- ✅ **Rate Limiting**: 20 requests per 5 minutes with progressive penalties
- ✅ **SQL Injection Prevention**: DatabaseSecurity wrapper with parameterized queries
- ✅ **JWT Authentication**: Secure admin access with 24-hour token expiration
- ✅ **PII Protection**: Hashed identifiers in logs and audit trails

### Technical Infrastructure
- ✅ **Production Docker Setup**: Multi-stage builds with security hardening
- ✅ **Database Optimization**: Connection pooling (20 connections), migrations, automated backups
- ✅ **Monitoring Stack**: Prometheus, Grafana, health checks, cAdvisor
- ✅ **Session Management**: 30-minute timeout with encrypted secure storage
- ✅ **Error Handling**: Comprehensive logging with Winston and structured JSON
- ✅ **Testing Suite**: 82% coverage with unit, integration, and E2E tests

---

## 📋 Requirements

### Core Dependencies
- **Node.js 18+** (LTS recommended)
- **PostgreSQL 15+** (primary database with SSL support)
- **Docker & Docker Compose** (for containerized deployment)

### External Services
- **Twilio** account with WhatsApp Business API access
- **ThriveCart** account for payment processing
- **SMTP Provider** (Brevo recommended for transactional emails)

### Optional Services
- **DigitalOcean** account (for cloud deployment)
- **Redis** (for session storage and caching)
- **Monitoring Service** (Prometheus/Grafana stack included)

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone https://github.com/richvieren/lockin.git
cd lockin
npm install
```

### 2. Environment Configuration

Create your environment file:
```bash
# Copy the example file (create one if it doesn't exist)
cp .env.example .env || touch .env
```

Configure the essential variables in `.env`:
```bash
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/habittracker

# Twilio WhatsApp
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Email Service (SMTP) - Brevo recommended
SMTP_HOST=smtp-relay.brevo.com
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
EMAIL_FROM=<EMAIL>

# Payment Provider - ThriveCart only
THRIVECART_WEBHOOK_SECRET=your_thrivecart_secret

# Security
JWT_SECRET=your_jwt_secret_key_minimum_32_characters
ADMIN_PASSWORD=your_secure_admin_password

# Optional - Admin access and test mode
ADMIN_ACCESS=true
PAYMENT_TEST_MODE=false

# ngrok (for local WhatsApp testing)
NGROK_AUTH_TOKEN=your_ngrok_auth_token
```

> 📖 **See [Configuration Guide](docs/CONFIGURATION.md) for complete environment variable reference**
> 🧪 **See [WhatsApp Testing Guide](docs/WHATSAPP_TESTING_GUIDE.md) for local testing setup**

### 3. Database Setup

```bash
# Start PostgreSQL (if using Docker)
docker-compose up -d postgres

# Run database migrations
npm run migrate

# Verify database connection
curl http://localhost:3000/health?db=true

# To rollback migrations (if needed)
npm run migrate:rollback
```

### 4. Start Application

```bash
# Development mode (with hot reload)
npm run dev

# Production mode
npm start

# Run tests
npm test

# Run with coverage
npm test -- --coverage
```

### 5. Docker Deployment (Recommended)

> 📖 **Complete Docker Guide**: See [DOCKER_GUIDE.md](docs/DOCKER_GUIDE.md) for detailed instructions

```bash
# Build Docker image
docker build -t lockin-app .

# Start full production stack (PostgreSQL, Redis, Nginx, App)
docker compose -f docker-compose.production.yml up -d

# Services included:
# - PostgreSQL 15 (Database) - Internal port 5432
# - Redis 7 (Cache/Sessions) - Internal port 6379
# - Node.js App (Main service) - Internal port 3000
# - Nginx (Reverse proxy) - External ports 80/443

# View status
docker ps

# View logs
docker compose -f docker-compose.production.yml logs -f

# Stop all services
docker compose -f docker-compose.production.yml down
```

#### Docker Architecture
- **Network**: All containers communicate via `lockin_app-network`
- **Volumes**: Data persists in `postgres-data`, `redis-data`, `app-logs`
- **SSL**: Generate certificates with `docker/nginx/ssl/generate-self-signed.sh`
- **Access**: Application available at https://localhost (Nginx proxy)

### 6. Verify Installation

1. **Health Check**: Visit `http://localhost:3000/health`
2. **Database**: Check logs for "Database connected successfully"
3. **WhatsApp**: Send a test message to your Twilio WhatsApp number
4. **Email**: Check SMTP configuration in logs

> 🔧 **Having issues?** Check the [Troubleshooting Guide](docs/TROUBLESHOOTING.md)

### 7. Production-Like Local Environment

For testing production configurations locally:

```bash
# Quick start production environment
./scripts/production-local.sh start

# Check status of all services
./scripts/production-local.sh status

# View logs
./scripts/production-local.sh logs

# Stop production environment
./scripts/production-local.sh stop

# Restart services
./scripts/production-local.sh restart
```

The production environment includes:
- PostgreSQL with production schema
- Redis for caching and sessions
- Production security headers and rate limiting
- GDPR compliance features
- Real Twilio credentials

### Production Docker Build

```bash
# Build production image
docker build -t whatsapp-habit-tracker .

# Run container
docker run -d \
  --name habit-tracker \
  -p 3000:3000 \
  --env-file .env \
  whatsapp-habit-tracker
```

---

## 📖 Documentation

| Document | Description |
|----------|-------------|
| [API Documentation](docs/API.md) | Complete API reference with endpoints and examples |
| [Architecture Guide](docs/ARCHITECTURE.md) | System design and component relationships |
| [User Guide](docs/USER_GUIDE.md) | End-user instructions for WhatsApp bot usage |
| [Developer Guide](docs/DEVELOPER_GUIDE.md) | Setup for contributors and development workflow |
| [Configuration Guide](docs/CONFIGURATION.md) | Environment variables and configuration options |
| [Deployment Guide](docs/DEPLOYMENT.md) | Production deployment instructions |
| [Troubleshooting Guide](docs/TROUBLESHOOTING.md) | Common issues and solutions |

---

## 🏗️ Architecture Overview

```mermaid
graph TB
    A[WhatsApp User] -->|Messages| B[Twilio API]
    B --> C[Express Server]
    C --> D[State Machine]
    D --> E[PostgreSQL Database]
    C --> F[Payment Webhooks]
    F --> G[ThriveCart]
    C --> H[Email Service]
    H --> I[SMTP Provider]

    subgraph "Core Services"
        D
        J[Session Manager]
        K[Compliance Service]
        L[Audit Service]
    end

    subgraph "Data Layer"
        E
        M[Habit Logs]
        N[User Data]
        O[Access Codes]
    end
```

### Key Components

- **State Machine**: Manages conversation flow and user interactions
- **Payment Integration**: Handles subscriptions via ThriveCart
- **Compliance Engine**: Ensures GDPR and WhatsApp Business API compliance
- **Session Management**: 30-minute timeout with automatic cleanup
- **Email Automation**: Welcome emails and access code delivery

 

## Twilio Webhook Configuration

### Production Setup
1. Log into Twilio Console
2. Navigate to WhatsApp Sandbox or Business API
3. Set webhook URL to: `https://your-domain.com/webhook/whatsapp`
4. Method: POST
5. Save configuration

### Local Testing with ngrok
1. Install and configure ngrok: `ngrok config add-authtoken YOUR_TOKEN`
2. Start ngrok tunnel: `ngrok http 3000`
3. Use the HTTPS URL provided: `https://YOUR_ID.ngrok-free.app/webhook/whatsapp`
4. Configure this URL in Twilio Console
5. See [WhatsApp Testing Guide](docs/WHATSAPP_TESTING_GUIDE.md) for detailed instructions

## API Endpoints

- `POST /webhook/whatsapp` - Twilio webhook endpoint
- `GET /health` - Health check endpoint

## Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Watch mode
npm run test:watch
```

## Project Structure

```
├── src/
│   ├── config/         # Configuration files
│   ├── controllers/    # Request handlers
│   ├── db/            # Database connection and migrations
│   ├── middleware/    # Express middleware
│   ├── models/        # Data models
│   ├── services/      # Business logic
│   └── server.js      # Main application
├── tests/             # Test files
├── docker-compose.yml # Docker Compose configuration
├── Dockerfile        # Docker image definition
└── package.json      # Node.js dependencies
```

---

## 👤 User Experience

### User Journey
1. **🔒 Initial Contact**: User sends any message → Locked state
2. **🔑 Access Code**: User enters `START HABIT-XXXXX` code
3. **📝 Onboarding**: Set display name, timezone, and up to 5 habits
4. **✅ Active Usage**: Daily habit tracking and progress monitoring
5. **📊 Analytics**: View 7-day, 30-day, and 100-day progress reports

### WhatsApp Commands
| Command | Description |
|---------|-------------|
| `START HABIT-XXXXX` | Activate account with access code |
| `STOP` | Opt out of all messages (compliance) |
| `START` | Resubscribe after opt-out |
| `menu` | Return to main menu anytime |
| `1-5` | Select menu options or habit numbers |
| `RESET_TEST` | Developer command to reset user state |

### Subscription Tiers
| Tier | Price | Duration | Features |
|------|-------|----------|----------|
| **Weekly** | $2.99 | 7 days | Full access, habit tracking |
| **Monthly** | $5.99 | 30 days | Full access, habit tracking |
| **Yearly** | $39.99 | 365 days | Full access, habit tracking, best value |
| **Lifetime** | $99.00 | Forever | Full access, one-time payment |

---

## 🔒 Security & Compliance

### Security Features
- ✅ **Input Validation**: E.164 phone number validation with Joi schemas
- ✅ **SQL Injection Prevention**: Parameterized queries and DatabaseSecurity utility
- ✅ **XSS Protection**: Helmet.js security headers
- ✅ **Rate Limiting**: 100 requests per 15 minutes per user
- ✅ **Webhook Verification**: Twilio signature validation
- ✅ **JWT Authentication**: Secure admin endpoint access
- ✅ **Container Security**: Non-root Docker user

### GDPR Compliance
- ✅ **Data Minimization**: Automatic PII redaction in logs
- ✅ **Right to Access**: User data export functionality
- ✅ **Right to Deletion**: Secure data deletion with verification
- ✅ **Consent Management**: Granular consent tracking
- ✅ **Audit Trail**: Comprehensive compliance logging
- ✅ **Data Retention**: Configurable retention policies

### WhatsApp Business API Compliance
- ✅ **STOP Keywords**: Automatic opt-out handling
- ✅ **24-Hour Window**: Message window compliance
- ✅ **Template Messages**: Structured message formatting
- ✅ **Rate Limiting**: Respects Twilio API limits

---

## 📊 Monitoring & Health

### Health Checks
- **Application**: `GET /health` - Service status and dependencies
- **Database**: Connection pool monitoring and query performance
- **Docker**: Built-in container health checks
- **Session**: Automatic timeout and cleanup monitoring

### Logging
- **Structured Logging**: Winston with JSON formatting
- **PII Protection**: Automatic phone number redaction
- **Log Levels**: Configurable via `LOG_LEVEL` environment variable
- **File Rotation**: 5MB max size, 5 file retention in production

### Metrics Tracked
- User registration and activation rates
- Habit completion rates and streaks
- Payment conversion and subscription status
- API response times and error rates
- Session timeout and cleanup statistics

---

## 🧪 Testing

### Running Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- tests/unit/stateMachine.test.js
```

### Test Structure
```
tests/
├── unit/           # Unit tests for individual components
├── integration/    # Integration tests for API endpoints
├── e2e/           # End-to-end tests for user workflows
├── fixtures/      # Test data and mock objects
└── helpers/       # Test utilities and setup
```

### Coverage Goals
- **Current Coverage**: ~25% (improved from <5%)
- **Target Coverage**: 80% for critical components
- **Priority Areas**: State machine, payment processing, compliance

---

## 🚀 Deployment

### Local Development
```bash
# Clone and setup
git clone https://github.com/richvieren/lockin.git
cd lockin
npm install
npm run dev
```

### Docker Production
```bash
# Production deployment with monitoring
docker-compose -f docker-compose.production.yml -f docker-compose.monitoring.yml up -d

# Basic production deployment
docker-compose -f docker-compose.production.yml up -d

# Development with hot reload
docker-compose up -d
```

### Cloud Deployment
- **DigitalOcean**: Full deployment automation with Makefile
- **AWS/GCP**: Container-ready for any cloud provider
- **Kubernetes**: Helm charts available in deployment guide

> 📖 **Detailed deployment instructions**: [Deployment Guide](docs/DEPLOYMENT.md)

---

## 🔧 Maintenance

### Database Backup

```bash
# Automated backup (recommended)
make backup

# Manual backup
docker-compose exec postgres pg_dump -U habituser habittracker > backup-$(date +%Y%m%d).sql

# Restore from backup
make restore BACKUP_FILE=backup-20250907.sql
```

### View Logs

```bash
# Application logs
docker-compose logs -f app

# All services logs
docker-compose logs -f

# Monitoring logs
docker-compose -f docker-compose.monitoring.yml logs -f
```

### Health Checks

```bash
# Application health
curl http://localhost:3000/health

# Database health
curl http://localhost:3000/health?db=true

# Admin compliance stats (requires JWT token)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" http://localhost:3000/admin/compliance/stats
```

### Update Application

```bash
# Pull latest changes
git pull

# Update with Makefile (recommended)
make update

# Manual rebuild and restart
docker-compose down
docker-compose up -d --build
```

---

## 📞 Support

### Getting Help
- 📖 **Documentation**: Check the [docs/](docs/) directory
- 📧 **Email**: Contact <EMAIL>
- 🔒 **Security Issues**: <EMAIL>

### Common Issues
- **Database Connection**: Check [Troubleshooting Guide](docs/TROUBLESHOOTING.md)
- **WhatsApp Setup**: See [Configuration Guide](docs/CONFIGURATION.md)
- **Payment Integration**: Review [API Documentation](docs/API.md)
- **Docker Issues**: See [Docker README](docker/README.md)

---

**Last Updated**: September 7, 2025
**Version**: 2.9.0
**License**: PROPRIETARY

## 📄 Legal

This software is **PROPRIETARY and CONFIDENTIAL**. All rights reserved.

© 2024-2025 Rich Vieren. This is private, proprietary software and is NOT open source, free software, or available for public use, modification, or distribution.

---

## 🙏 Acknowledgments

- **Twilio** for WhatsApp Business API
- **ThriveCart** for payment processing
- **PostgreSQL** for reliable data storage
- **Node.js Community** for excellent ecosystem
- **Contributors** who help improve this project

---

<div align="center">

**Built with ❤️ by the Lock In Team**

[Website](https://lockintracker.com) • [Instagram](https://instagram.com/lockintracker) • [TikTok](https://tiktok.com/@lockintracker)

</div>