const menuHandlers = require('../../../src/services/handlers/menuHandlers');
const Habit = require('../../../src/models/Habit');
const User = require('../../../src/models/User');
const { STATES } = require('../../../src/config/constants');
const logger = require('../../../src/config/logger');

jest.mock('../../../src/models/Habit');
jest.mock('../../../src/models/User');
jest.mock('../../../src/config/logger');

describe('menuHandlers', () => {
  const mockUser = {
    id: 1,
    phone: '+1234567890',
    status: 'ACTIVE',
    current_state: STATES.MAIN_MENU,
    display_name: 'John'
  };

  const mockContext = {
    habitCount: 0,
    completedToday: false,
    currentDate: '2024-01-01'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('handleMainMenu', () => {
    it('should show menu for user with no habits', async () => {
      Habit.getUserHabits.mockResolvedValue([]);
      Habit.hasLoggedToday.mockResolvedValue(false);

      const result = await menuHandlers.handleMainMenu(mockUser, '', mockContext);

      expect(result.response).toContain('Welcome');
      expect(result.response).toContain('1️⃣ Setup New Habit');
      expect(result.response).not.toContain('2️⃣ Log Progress');
      expect(result.state).toBe(STATES.MAIN_MENU);
    });

    it('should show full menu for user with habits', async () => {
      const mockHabits = [
        { id: 1, name: 'Exercise', user_id: 1 },
        { id: 2, name: 'Reading', user_id: 1 }
      ];
      Habit.getUserHabits.mockResolvedValue(mockHabits);
      Habit.hasLoggedToday.mockResolvedValue(false);

      const result = await menuHandlers.handleMainMenu(mockUser, '', mockContext);

      expect(result.response).toContain('1️⃣ Setup New Habit');
      expect(result.response).toContain('2️⃣ Log Progress');
      expect(result.response).toContain('3️⃣ View Progress');
      expect(result.response).toContain('4️⃣ View Habits');
      expect(result.response).toContain('5️⃣ Reset Account');
      expect(result.context.habitCount).toBe(2);
    });

    it('should show completion message when habits logged today', async () => {
      const mockHabits = [
        { id: 1, name: 'Exercise', user_id: 1 }
      ];
      Habit.getUserHabits.mockResolvedValue(mockHabits);
      Habit.hasLoggedToday.mockResolvedValue(true);

      const result = await menuHandlers.handleMainMenu(mockUser, '', mockContext);

      expect(result.response).toContain('✅ Great job!');
      expect(result.response).toContain("You've completed your habits for today");
      expect(result.context.completedToday).toBe(true);
    });

    it('should handle setup new habit selection', async () => {
      Habit.getUserHabits.mockResolvedValue([]);

      const result = await menuHandlers.handleMainMenu(mockUser, '1', mockContext);

      expect(result.state).toBe(STATES.HABIT_SETUP_NAME);
      expect(result.response).toContain('What habit would you like to track?');
    });

    it('should handle log progress selection', async () => {
      const mockHabits = [
        { id: 1, name: 'Exercise', user_id: 1 }
      ];
      Habit.getUserHabits.mockResolvedValue(mockHabits);
      Habit.hasLoggedToday.mockResolvedValue(false);

      const result = await menuHandlers.handleMainMenu(mockUser, '2', { habitCount: 1 });

      expect(result.state).toBe(STATES.LOG_HABIT_SELECT);
      expect(result.response).toContain('Which habits did you complete today?');
    });

    it('should handle view progress selection', async () => {
      const result = await menuHandlers.handleMainMenu(mockUser, '3', { habitCount: 2 });

      expect(result.state).toBe(STATES.VIEW_PROGRESS);
    });

    it('should handle view habits selection', async () => {
      const result = await menuHandlers.handleMainMenu(mockUser, '4', { habitCount: 2 });

      expect(result.state).toBe(STATES.VIEW_HABITS);
    });

    it('should handle reset account selection', async () => {
      const result = await menuHandlers.handleMainMenu(mockUser, '5', { habitCount: 1 });

      expect(result.state).toBe(STATES.RESET_CONFIRM);
      expect(result.response).toContain('Are you sure');
      expect(result.response).toContain('This will delete all your habits');
    });

    it('should reject invalid menu option', async () => {
      Habit.getUserHabits.mockResolvedValue([{ id: 1, name: 'Exercise' }]);

      const result = await menuHandlers.handleMainMenu(mockUser, '9', { habitCount: 1 });

      expect(result.response).toContain("I didn't understand that");
      expect(result.state).toBe(STATES.MAIN_MENU);
    });

    it('should handle 5 habits limit', async () => {
      const mockHabits = Array(5).fill(null).map((_, i) => ({
        id: i + 1,
        name: `Habit ${i + 1}`,
        user_id: 1
      }));
      Habit.getUserHabits.mockResolvedValue(mockHabits);

      const result = await menuHandlers.handleMainMenu(mockUser, '', mockContext);

      expect(result.response).toContain('(Maximum 5 reached)');
      expect(result.response).not.toContain('1️⃣ Setup New Habit');
    });

    it('should handle errors gracefully', async () => {
      Habit.getUserHabits.mockRejectedValue(new Error('Database error'));

      const result = await menuHandlers.handleMainMenu(mockUser, '', mockContext);

      expect(logger.error).toHaveBeenCalledWith(
        'Error in main menu handler',
        expect.objectContaining({ error: 'Database error' })
      );
      expect(result.response).toContain('error occurred');
      expect(result.state).toBe(STATES.MAIN_MENU);
    });
  });

  describe('handleQuickActions', () => {
    it('should handle quick log command', async () => {
      const result = await menuHandlers.handleQuickActions(mockUser, 'LOG', mockContext);

      expect(result).toBeDefined();
      expect(result.state).toBe(STATES.LOG_HABIT_SELECT);
    });

    it('should handle quick progress command', async () => {
      const result = await menuHandlers.handleQuickActions(mockUser, 'PROGRESS', mockContext);

      expect(result).toBeDefined();
      expect(result.state).toBe(STATES.VIEW_PROGRESS);
    });

    it('should handle quick habits command', async () => {
      const result = await menuHandlers.handleQuickActions(mockUser, 'HABITS', mockContext);

      expect(result).toBeDefined();
      expect(result.state).toBe(STATES.VIEW_HABITS);
    });

    it('should handle menu command', async () => {
      const result = await menuHandlers.handleQuickActions(mockUser, 'MENU', mockContext);

      expect(result).toBeDefined();
      expect(result.state).toBe(STATES.MAIN_MENU);
    });

    it('should return null for non-quick action', async () => {
      const result = await menuHandlers.handleQuickActions(mockUser, 'random text', mockContext);

      expect(result).toBeNull();
    });
  });

  describe('formatMenuResponse', () => {
    it('should format menu with user name', () => {
      const options = ['Option 1', 'Option 2'];
      const result = menuHandlers.formatMenuResponse(mockUser, options, mockContext);

      expect(result).toContain('Hi John');
      expect(result).toContain('Option 1');
      expect(result).toContain('Option 2');
    });

    it('should format menu without user name', () => {
      const userWithoutName = { ...mockUser, display_name: null };
      const options = ['Option 1'];
      
      const result = menuHandlers.formatMenuResponse(userWithoutName, options, mockContext);

      expect(result).toContain('Welcome back');
      expect(result).not.toContain('Hi');
    });

    it('should include completion message when habits completed', () => {
      const options = ['Option 1'];
      const contextWithCompletion = { ...mockContext, completedToday: true };
      
      const result = menuHandlers.formatMenuResponse(mockUser, options, contextWithCompletion);

      expect(result).toContain('✅');
      expect(result).toContain('completed');
    });
  });

  describe('validateMenuOption', () => {
    it('should validate numeric options within range', () => {
      expect(menuHandlers.validateMenuOption('1', 5)).toBe(true);
      expect(menuHandlers.validateMenuOption('5', 5)).toBe(true);
      expect(menuHandlers.validateMenuOption('6', 5)).toBe(false);
      expect(menuHandlers.validateMenuOption('0', 5)).toBe(false);
    });

    it('should handle invalid input', () => {
      expect(menuHandlers.validateMenuOption('abc', 5)).toBe(false);
      expect(menuHandlers.validateMenuOption('', 5)).toBe(false);
      expect(menuHandlers.validateMenuOption(null, 5)).toBe(false);
    });
  });

  describe('getMenuOptions', () => {
    it('should return options based on habit count', () => {
      const options0 = menuHandlers.getMenuOptions(0);
      expect(options0).toHaveLength(1);
      expect(options0[0]).toContain('Setup');

      const options2 = menuHandlers.getMenuOptions(2);
      expect(options2).toHaveLength(5);
      expect(options2[1]).toContain('Log Progress');

      const options5 = menuHandlers.getMenuOptions(5);
      expect(options5).toHaveLength(4);
      expect(options5[0]).toContain('Log Progress');
    });

    it('should include disabled message at max habits', () => {
      const options = menuHandlers.getMenuOptions(5);
      expect(options.some(opt => opt.includes('Maximum 5 reached'))).toBe(true);
    });
  });
});