const paymentService = require('../../src/services/paymentService');
const pool = require('../../src/db/connection');
const emailService = require('../../src/services/emailService');
const logger = require('../../src/config/logger');
const crypto = require('crypto');

jest.mock('../../src/db/connection');
jest.mock('../../src/services/emailService');
jest.mock('../../src/config/logger');

describe('paymentService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateAccessCode', () => {
    it('should validate a valid access code', async () => {
      const mockCode = {
        id: 1,
        code: 'TEST123',
        uses_remaining: 5,
        expires_at: new Date(Date.now() + 86400000) // Tomorrow
      };

      pool.query.mockResolvedValue({ rows: [mockCode] });

      const result = await paymentService.validateAccessCode('TEST123');

      expect(result.valid).toBe(true);
      expect(result.code).toEqual(mockCode);
      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM access_codes'),
        ['TEST123']
      );
    });

    it('should reject expired code', async () => {
      const expiredCode = {
        id: 1,
        code: 'EXPIRED',
        uses_remaining: 5,
        expires_at: new Date(Date.now() - 86400000) // Yesterday
      };

      pool.query.mockResolvedValue({ rows: [expiredCode] });

      const result = await paymentService.validateAccessCode('EXPIRED');

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('expired');
    });

    it('should reject code with no uses remaining', async () => {
      const usedCode = {
        id: 1,
        code: 'USED',
        uses_remaining: 0,
        expires_at: new Date(Date.now() + 86400000)
      };

      pool.query.mockResolvedValue({ rows: [usedCode] });

      const result = await paymentService.validateAccessCode('USED');

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('no_uses_remaining');
    });

    it('should handle code not found', async () => {
      pool.query.mockResolvedValue({ rows: [] });

      const result = await paymentService.validateAccessCode('NOTFOUND');

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('not_found');
    });

    it('should handle database errors', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      const result = await paymentService.validateAccessCode('TEST');

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('error');
      expect(logger.error).toHaveBeenCalledWith(
        'Error validating access code',
        expect.objectContaining({ error: 'DB error' })
      );
    });
  });

  describe('useAccessCode', () => {
    it('should use access code successfully', async () => {
      const mockClient = {
        query: jest.fn()
          .mockResolvedValueOnce() // BEGIN
          .mockResolvedValueOnce({ rows: [{ 
            id: 1, 
            code: 'TEST123',
            uses_remaining: 5,
            expires_at: new Date(Date.now() + 86400000)
          }] }) // SELECT code
          .mockResolvedValueOnce({ rows: [] }) // UPDATE code
          .mockResolvedValueOnce({ rows: [] }) // INSERT code_usage
          .mockResolvedValueOnce({ rows: [{ id: 1, status: 'ACTIVE' }] }) // UPDATE user
          .mockResolvedValueOnce(), // COMMIT
        release: jest.fn()
      };

      pool.connect.mockResolvedValue(mockClient);

      const result = await paymentService.useAccessCode(1, 'TEST123');

      expect(result.success).toBe(true);
      expect(mockClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockClient.query).toHaveBeenCalledWith('COMMIT');
      expect(mockClient.release).toHaveBeenCalled();
    });

    it('should handle invalid code', async () => {
      const mockClient = {
        query: jest.fn()
          .mockResolvedValueOnce() // BEGIN
          .mockResolvedValueOnce({ rows: [] }) // SELECT code - not found
          .mockResolvedValueOnce(), // ROLLBACK
        release: jest.fn()
      };

      pool.connect.mockResolvedValue(mockClient);

      const result = await paymentService.useAccessCode(1, 'INVALID');

      expect(result.success).toBe(false);
      expect(result.message).toContain('Invalid or expired');
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
    });

    it('should handle transaction errors', async () => {
      const mockClient = {
        query: jest.fn()
          .mockResolvedValueOnce() // BEGIN
          .mockRejectedValueOnce(new Error('DB error')),
        release: jest.fn()
      };

      pool.connect.mockResolvedValue(mockClient);

      const result = await paymentService.useAccessCode(1, 'TEST');

      expect(result.success).toBe(false);
      expect(result.message).toContain('error processing');
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('generateAccessCode', () => {
    it('should generate unique access code', async () => {
      // First check returns existing code, second returns nothing
      pool.query
        .mockResolvedValueOnce({ rows: [{ code: 'EXISTS' }] })
        .mockResolvedValueOnce({ rows: [] });

      const code = await paymentService.generateAccessCode();

      expect(code).toBeDefined();
      expect(code).toHaveLength(8);
      expect(code).toMatch(/^[A-Z0-9]+$/);
      expect(pool.query).toHaveBeenCalledTimes(2);
    });

    it('should retry on collision', async () => {
      // Simulate multiple collisions
      pool.query
        .mockResolvedValueOnce({ rows: [{ code: 'EXISTS1' }] })
        .mockResolvedValueOnce({ rows: [{ code: 'EXISTS2' }] })
        .mockResolvedValueOnce({ rows: [] });

      const code = await paymentService.generateAccessCode();

      expect(code).toBeDefined();
      expect(pool.query).toHaveBeenCalledTimes(3);
    });
  });

  describe('createAccessCodes', () => {
    it('should create multiple access codes', async () => {
      pool.query
        .mockResolvedValueOnce({ rows: [] }) // Check code 1
        .mockResolvedValueOnce({ rows: [{ id: 1, code: 'CODE1' }] }) // Insert code 1
        .mockResolvedValueOnce({ rows: [] }) // Check code 2
        .mockResolvedValueOnce({ rows: [{ id: 2, code: 'CODE2' }] }); // Insert code 2

      const codes = await paymentService.createAccessCodes('order123', '<EMAIL>', 2);

      expect(codes).toHaveLength(2);
      expect(codes[0]).toHaveProperty('code');
      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO access_codes'),
        expect.arrayContaining(['order123', '<EMAIL>'])
      );
    });

    it('should handle creation errors', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      const codes = await paymentService.createAccessCodes('order123', '<EMAIL>', 1);

      expect(codes).toEqual([]);
      expect(logger.error).toHaveBeenCalledWith(
        'Error creating access codes',
        expect.any(Object)
      );
    });
  });

  describe('processPayment', () => {
    it('should process successful payment', async () => {
      const paymentData = {
        customer: {
          email: '<EMAIL>',
          name: 'John Doe'
        },
        order: {
          id: 'order123',
          total: 9.99
        }
      };

      pool.query
        .mockResolvedValueOnce({ rows: [] }) // Check existing payment
        .mockResolvedValueOnce({ rows: [{ id: 1 }] }) // Insert payment
        .mockResolvedValueOnce({ rows: [] }) // Check code
        .mockResolvedValueOnce({ rows: [{ id: 1, code: 'NEWCODE' }] }); // Insert code

      emailService.sendPurchaseEmail = jest.fn().mockResolvedValue(true);

      const result = await paymentService.processPayment(paymentData);

      expect(result.success).toBe(true);
      expect(result.codes).toHaveLength(1);
      expect(emailService.sendPurchaseEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        expect.objectContaining({
          name: 'John Doe',
          codes: expect.any(Array)
        })
      );
    });

    it('should handle duplicate payments', async () => {
      pool.query.mockResolvedValueOnce({ 
        rows: [{ id: 1, order_id: 'order123' }] 
      });

      const result = await paymentService.processPayment({
        order: { id: 'order123' }
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('already processed');
    });

    it('should handle payment processing errors', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      const result = await paymentService.processPayment({
        customer: { email: '<EMAIL>' },
        order: { id: 'order123' }
      });

      expect(result.success).toBe(false);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('verifyThriveCartSignature', () => {
    it('should verify valid signature', () => {
      const secret = 'test-secret';
      process.env.THRIVECART_SECRET = secret;
      
      const payload = { test: 'data' };
      const expectedSig = crypto
        .createHmac('sha256', secret)
        .update(JSON.stringify(payload))
        .digest('hex');

      const result = paymentService.verifyThriveCartSignature(payload, expectedSig);

      expect(result).toBe(true);
    });

    it('should reject invalid signature', () => {
      process.env.THRIVECART_SECRET = 'test-secret';
      
      const result = paymentService.verifyThriveCartSignature(
        { test: 'data' },
        'invalid-signature'
      );

      expect(result).toBe(false);
    });

    it('should handle missing secret', () => {
      delete process.env.THRIVECART_SECRET;
      
      const result = paymentService.verifyThriveCartSignature(
        { test: 'data' },
        'signature'
      );

      expect(result).toBe(false);
      expect(logger.error).toHaveBeenCalledWith('THRIVECART_SECRET not configured');
    });
  });

  describe('getSubscriptionStatus', () => {
    it('should get active subscription', async () => {
      const mockSubscription = {
        id: 1,
        user_id: 1,
        status: 'active',
        expires_at: new Date(Date.now() + 86400000)
      };

      pool.query.mockResolvedValue({ rows: [mockSubscription] });

      const result = await paymentService.getSubscriptionStatus(1);

      expect(result.active).toBe(true);
      expect(result.subscription).toEqual(mockSubscription);
    });

    it('should detect expired subscription', async () => {
      const expiredSub = {
        id: 1,
        user_id: 1,
        status: 'active',
        expires_at: new Date(Date.now() - 86400000)
      };

      pool.query.mockResolvedValue({ rows: [expiredSub] });

      const result = await paymentService.getSubscriptionStatus(1);

      expect(result.active).toBe(false);
      expect(result.reason).toBe('expired');
    });

    it('should handle no subscription', async () => {
      pool.query.mockResolvedValue({ rows: [] });

      const result = await paymentService.getSubscriptionStatus(1);

      expect(result.active).toBe(false);
      expect(result.reason).toBe('not_found');
    });

    it('should handle cancelled subscription', async () => {
      const cancelledSub = {
        id: 1,
        user_id: 1,
        status: 'cancelled',
        expires_at: new Date(Date.now() + 86400000)
      };

      pool.query.mockResolvedValue({ rows: [cancelledSub] });

      const result = await paymentService.getSubscriptionStatus(1);

      expect(result.active).toBe(false);
      expect(result.reason).toBe('cancelled');
    });
  });

  describe('handleRefund', () => {
    it('should process refund successfully', async () => {
      pool.query
        .mockResolvedValueOnce({ rows: [{ id: 1, status: 'completed' }] }) // Find payment
        .mockResolvedValueOnce({ rows: [] }) // Update payment
        .mockResolvedValueOnce({ rows: [] }); // Deactivate codes

      const result = await paymentService.handleRefund('order123');

      expect(result.success).toBe(true);
      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE payments SET status = $1'),
        expect.arrayContaining(['refunded'])
      );
    });

    it('should handle payment not found', async () => {
      pool.query.mockResolvedValue({ rows: [] });

      const result = await paymentService.handleRefund('notfound');

      expect(result.success).toBe(false);
      expect(result.message).toContain('Payment not found');
    });
  });
});