const SubscriptionService = require('../../src/services/subscriptionService');
const pool = require('../../src/db/connection');
const logger = require('../../src/config/logger');
const sqlIntervalBuilder = require('../../src/utils/sqlIntervalBuilder');
const DatabaseSecurity = require('../../src/utils/databaseSecurity');

jest.mock('../../src/db/connection');
jest.mock('../../src/config/logger');
jest.mock('../../src/utils/sqlIntervalBuilder');
jest.mock('../../src/utils/databaseSecurity');

describe('SubscriptionService', () => {
  let service;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
    service = require('../../src/services/subscriptionService');
    // Mock current date for consistent testing
    jest.useFakeTimers().setSystemTime(new Date('2024-01-15T00:00:00Z'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('checkSubscriptionStatus', () => {
    it('should return active subscription', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        subscription_type: 'monthly',
        status: 'active',
        expires_at: '2024-02-15T00:00:00Z'
      };
      DatabaseSecurity.validateParameters.mockReturnValue(['<EMAIL>']);
      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await service.checkSubscriptionStatus('<EMAIL>');

      expect(DatabaseSecurity.validateParameters).toHaveBeenCalledWith(['<EMAIL>']);
      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT id, email, subscription_type, status, expires_at'),
        expect.any(Array)
      );
      expect(result).toEqual({
        hasAccess: true,
        status: 'active',
        user: mockUser,
        expiresAt: new Date('2024-02-15T00:00:00Z'),
        message: 'Active until Mon Feb 15 2024'
      });
    });

    it('should return expired subscription and mark as expired', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        subscription_type: 'monthly',
        status: 'active',
        expires_at: '2024-01-10T00:00:00Z' // Past date
      };
      DatabaseSecurity.validateParameters.mockReturnValue(['<EMAIL>']);
      pool.query
        .mockResolvedValueOnce({ rows: [mockUser] }) // Initial query
        .mockResolvedValueOnce({ rows: [mockUser] }); // markExpired query

      const result = await service.checkSubscriptionStatus('<EMAIL>');

      expect(pool.query).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        hasAccess: false,
        status: 'expired',
        user: mockUser,
        expiresAt: new Date('2024-01-10T00:00:00Z'),
        message: 'Subscription expired on Tue Jan 10 2024'
      });
    });

    it('should handle non-active status', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        status: 'paused',
        expires_at: '2024-02-15T00:00:00Z'
      };
      DatabaseSecurity.validateParameters.mockReturnValue(['<EMAIL>']);
      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await service.checkSubscriptionStatus('<EMAIL>');

      expect(result).toEqual({
        hasAccess: false,
        status: 'paused',
        user: mockUser,
        expiresAt: new Date('2024-02-15T00:00:00Z'),
        message: 'Subscription status: paused'
      });
    });

    it('should handle lifetime/no expiration', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        subscription_type: 'lifetime',
        status: 'active'
        // No expires_at
      };
      DatabaseSecurity.validateParameters.mockReturnValue(['<EMAIL>']);
      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await service.checkSubscriptionStatus('<EMAIL>');

      expect(result).toEqual({
        hasAccess: true,
        status: 'active',
        user: mockUser,
        message: 'Active subscription (no expiration)'
      });
    });

    it('should handle user not found', async () => {
      DatabaseSecurity.validateParameters.mockReturnValue(['<EMAIL>']);
      pool.query.mockResolvedValue({ rows: [] });

      const result = await service.checkSubscriptionStatus('<EMAIL>');

      expect(result).toEqual({
        hasAccess: false,
        status: 'not_found',
        message: 'User not found'
      });
    });

    it('should handle error gracefully', async () => {
      DatabaseSecurity.validateParameters.mockReturnValue(['<EMAIL>']);
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      const result = await service.checkSubscriptionStatus('<EMAIL>');

      expect(logger.error).toHaveBeenCalledWith('Error checking subscription status', { error: 'DB error', email: '<EMAIL>' });
      expect(result).toEqual({
        hasAccess: false,
        status: 'error',
        message: 'Error checking subscription status'
      });
    });
  });

  describe('checkSubscriptionByAccessCode', () => {
    it('should check subscription via access code successfully', async () => {
      const mockUser = { id: 1, email: '<EMAIL>', code: 'ACCESS123' };
      DatabaseSecurity.validateParameters.mockReturnValue(['ACCESS123']);
      pool.query.mockResolvedValue({ rows: [mockUser] });
      // Mock the delegated call
      const checkStatusMock = jest.spyOn(service, 'checkSubscriptionStatus').mockResolvedValue({ hasAccess: true });

      const result = await service.checkSubscriptionByAccessCode('ACCESS123');

      expect(DatabaseSecurity.validateParameters).toHaveBeenCalledWith(['ACCESS123']);
      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('INNER JOIN access_codes ac ON pu.id = ac.paid_user_id'),
        expect.any(Array)
      );
      expect(checkStatusMock).toHaveBeenCalledWith('<EMAIL>');
      expect(result).toEqual({ hasAccess: true });
    });

    it('should return invalid code if not found', async () => {
      DatabaseSecurity.validateParameters.mockReturnValue(['ACCESS123']);
      pool.query.mockResolvedValue({ rows: [] });

      const result = await service.checkSubscriptionByAccessCode('ACCESS123');

      expect(result).toEqual({
        hasAccess: false,
        status: 'invalid_code',
        message: 'Access code not found or inactive'
      });
    });

    it('should handle error', async () => {
      DatabaseSecurity.validateParameters.mockReturnValue(['ACCESS123']);
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      const result = await service.checkSubscriptionByAccessCode('ACCESS123');

      expect(logger.error).toHaveBeenCalledWith('Error checking subscription by access code', { error: 'DB error', accessCode: 'ACCESS123' });
      expect(result).toEqual({
        hasAccess: false,
        status: 'error',
        message: 'Error checking access code'
      });
    });
  });

  describe('markSubscriptionExpired', () => {
    it('should mark subscription expired and deactivate codes', async () => {
      DatabaseSecurity.validateParameters
        .mockReturnValueOnce([1]) // paid_users update
        .mockReturnValueOnce([1]); // access_codes update
      pool.query
        .mockResolvedValueOnce({ rows: [] }) // paid_users
        .mockResolvedValueOnce({ rows: [] }); // access_codes

      await service.markSubscriptionExpired(1);

      expect(DatabaseSecurity.validateParameters).toHaveBeenCalledTimes(2);
      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining("UPDATE paid_users SET status = 'expired'"),
        expect.any(Array)
      );
      expect(pool.query).toHaveBeenCalledWith(
        "UPDATE access_codes SET is_active = false WHERE paid_user_id = $1",
        expect.any(Array)
      );
      expect(logger.info).toHaveBeenCalledWith('Marked subscription as expired', { userId: 1 });
    });

    it('should throw error on failure', async () => {
      DatabaseSecurity.validateParameters.mockReturnValue([1]);
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(service.markSubscriptionExpired(1)).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error marking subscription as expired', { error: 'DB error', userId: 1 });
    });
  });

  describe('findExpiredSubscriptions', () => {
    it('should find expired active subscriptions', async () => {
      const mockExpired = [{ id: 1, email: '<EMAIL>', expires_at: '2024-01-10' }];
      DatabaseSecurity.isQuerySafe.mockReturnValue(true);
      pool.query.mockResolvedValue({ rows: mockExpired });

      const result = await service.findExpiredSubscriptions();

      expect(DatabaseSecurity.isQuerySafe).toHaveBeenCalledWith(expect.stringContaining('WHERE expires_at < NOW()'));
      expect(result).toEqual(mockExpired);
    });

    it('should throw on query validation failure', async () => {
      DatabaseSecurity.isQuerySafe.mockReturnValue(false);

      await expect(service.findExpiredSubscriptions()).rejects.toThrow('Query validation failed');
    });

    it('should throw on DB error', async () => {
      DatabaseSecurity.isQuerySafe.mockReturnValue(true);
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(service.findExpiredSubscriptions()).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error finding expired subscriptions', { error: 'DB error' });
    });
  });

  describe('processExpiredSubscriptions', () => {
    it('should process expired subscriptions', async () => {
      const mockExpired = [{ id: 1, email: '<EMAIL>' }];
      jest.spyOn(service, 'findExpiredSubscriptions').mockResolvedValue(mockExpired);
      jest.spyOn(service, 'markSubscriptionExpired');

      const result = await service.processExpiredSubscriptions();

      expect(service.findExpiredSubscriptions).toHaveBeenCalled();
      expect(service.markSubscriptionExpired).toHaveBeenCalledWith(1);
      expect(logger.info).toHaveBeenCalledWith('Found 1 expired subscriptions to process');
      expect(result).toEqual({
        processed: 1,
        expired: [{ email: '<EMAIL>', expiresAt: '2024-01-10T00:00:00Z', subscriptionType: 'monthly' }]
      });
    });

    it('should log no expired found', async () => {
      jest.spyOn(service, 'findExpiredSubscriptions').mockResolvedValue([]);

      const result = await service.processExpiredSubscriptions();

      expect(logger.info).toHaveBeenCalledWith('No expired subscriptions found');
      expect(result).toEqual({ processed: 0, expired: [] });
    });

    it('should throw on error', async () => {
      jest.spyOn(service, 'findExpiredSubscriptions').mockRejectedValue(new Error('Error'));
      jest.spyOn(logger, 'error');

      await expect(service.processExpiredSubscriptions()).rejects.toThrow('Error');
      expect(logger.error).toHaveBeenCalledWith('Error processing expired subscriptions', { error: 'Error' });
    });
  });

  describe('getExpiringSubscriptions', () => {
    it('should get subscriptions expiring in 7 days', async () => {
      const mockExpiring = [{ id: 1, email: '<EMAIL>', expires_at: '2024-01-22' }];
      DatabaseSecurity.validateParameters.mockReturnValue([7]);
      pool.query.mockResolvedValue({ rows: mockExpiring });

      const result = await service.getExpiringSubscriptions(7);

      expect(DatabaseSecurity.validateParameters).toHaveBeenCalledWith([7]);
      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('make_interval(days => $1)'),
        expect.any(Array)
      );
      expect(result).toEqual(mockExpiring);
    });

    it('should throw on error', async () => {
      DatabaseSecurity.validateParameters.mockReturnValue([7]);
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(service.getExpiringSubscriptions()).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error getting expiring subscriptions', { error: 'DB error' });
    });
  });

  describe('extendSubscription', () => {
    it('should extend monthly subscription', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        subscription_type: 'monthly',
        expires_at: '2024-01-15T00:00:00Z',
        billing_frequency_days: 30
      };
      DatabaseSecurity.buildSelectQuery.mockReturnValue('SELECT * FROM paid_users WHERE email = $1');
      DatabaseSecurity.validateParameters
        .mockReturnValueOnce(['<EMAIL>']) // Select
        .mockReturnValueOnce(['2024-02-14T00:00:00Z', '2024-03-15T00:00:00Z', '<EMAIL>']); // Update
      pool.query
        .mockResolvedValueOnce({ rows: [mockUser] }) // Select
        .mockResolvedValueOnce({ rows: [] }); // Update

      const result = await service.extendSubscription('<EMAIL>');

      expect(DatabaseSecurity.buildSelectQuery).toHaveBeenCalledWith('paid_users', ['*'], 'email');
      expect(result).toEqual({
        success: true,
        newExpiresAt: new Date('2024-02-14T00:00:00.000Z'),
        newNextBilling: new Date('2024-03-15T00:00:00.000Z')
      });
      expect(logger.info).toHaveBeenCalledWith('Extended subscription', expect.any(Object));
    });

    it('should handle lifetime subscription', async () => {
      const mockUser = { id: 1, email: '<EMAIL>', subscription_type: 'lifetime' };
      DatabaseSecurity.buildSelectQuery.mockReturnValue('SELECT * FROM paid_users WHERE email = $1');
      DatabaseSecurity.validateParameters.mockReturnValue(['<EMAIL>']);
      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await service.extendSubscription('<EMAIL>');

      expect(result).toEqual({
        success: true,
        message: 'Lifetime subscription - no extension needed'
      });
      expect(logger.info).toHaveBeenCalledWith('Attempted to extend lifetime subscription - no action needed', { email: '<EMAIL>' });
    });

    it('should throw user not found', async () => {
      DatabaseSecurity.buildSelectQuery.mockReturnValue('SELECT * FROM paid_users WHERE email = $1');
      DatabaseSecurity.validateParameters.mockReturnValue(['<EMAIL>']);
      pool.query.mockResolvedValueOnce({ rows: [] });

      await expect(service.extendSubscription('<EMAIL>')).rejects.toThrow('User not found');
    });

    it('should throw on DB error', async () => {
      DatabaseSecurity.buildSelectQuery.mockReturnValue('SELECT * FROM paid_users WHERE email = $1');
      DatabaseSecurity.validateParameters.mockReturnValue(['<EMAIL>']);
      pool.query.mockRejectedValueOnce(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(service.extendSubscription('<EMAIL>')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error extending subscription', { error: 'DB error', email: '<EMAIL>' });
    });
  });

  describe('getRenewalUrl', () => {
    it('should return correct URL for subscription type', () => {
      process.env.THRIVECART_MONTHLY_URL = 'https://thrivecart.com/monthly';
      const result = service.getRenewalUrl('monthly');

      expect(result).toBe('https://thrivecart.com/monthly');
    });

    it('should add affiliate code', () => {
      process.env.THRIVECART_WEEKLY_URL = 'https://thrivecart.com/weekly';
      const result = service.getRenewalUrl('weekly', 'AFF123');

      expect(result).toBe('https://thrivecart.com/weekly?affiliate=AFF123');
    });

    it('should fallback to monthly for unknown type', () => {
      process.env.THRIVECART_MONTHLY_URL = 'https://thrivecart.com/monthly';
      const result = service.getRenewalUrl('unknown');

      expect(result).toBe('https://thrivecart.com/monthly');
    });

    it('should use env defaults if not set', () => {
      delete process.env.THRIVECART_YEARLY_URL;
      const result = service.getRenewalUrl('yearly');

      expect(result).toBe('https://aeon.thrivecart.com/annual-subscription');
    });
  });

  describe('createRenewalMessage', () => {
    it('should create renewal message for expired monthly', () => {
      const mockUser = { subscription_type: 'monthly' };
      process.env.THRIVECART_MONTHLY_URL = 'https://thrivecart.com/monthly';

      const result = service.createRenewalMessage(mockUser);

      expect(result.message).toContain('Your Monthly subscription has expired');
      expect(result.renewalUrl).toBe('https://thrivecart.com/monthly');
      expect(result.subscriptionType).toBe('monthly');
    });

    it('should handle lifetime no renewal', () => {
      const mockUser = { subscription_type: 'lifetime' };

      const result = service.createRenewalMessage(mockUser);

      expect(result.message).toBe("You have lifetime access! No renewal needed.");
      expect(result.renewalUrl).toBeNull();
    });
  });
});