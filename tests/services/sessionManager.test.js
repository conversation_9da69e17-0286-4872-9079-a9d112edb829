const sessionManager = require('../../src/services/sessionManager');
const User = require('../../src/models/User');
const logger = require('../../src/config/logger');
const { STATES } = require('../../src/config/constants');

jest.mock('../../src/models/User');
jest.mock('../../src/config/logger');

describe('sessionManager', () => {
  const mockUser = {
    id: 1,
    phone: '+1234567890',
    status: 'ACTIVE',
    current_state: STATES.MAIN_MENU,
    session_context: {},
    last_active: new Date()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Clear session cache
    sessionManager.sessions.clear();
  });

  describe('getOrCreateSession', () => {
    it('should create new session for user without one', async () => {
      const session = await sessionManager.getOrCreateSession(mockUser);

      expect(session).toMatchObject({
        user: mockUser,
        context: {},
        lastActivity: expect.any(Date)
      });
      expect(sessionManager.sessions.has(mockUser.id)).toBe(true);
    });

    it('should return existing session within timeout', async () => {
      // Create initial session
      const session1 = await sessionManager.getOrCreateSession(mockUser);
      
      // Get same session
      const session2 = await sessionManager.getOrCreateSession(mockUser);

      expect(session1).toBe(session2);
      expect(sessionManager.sessions.size).toBe(1);
    });

    it('should detect expired session', async () => {
      // Create session
      await sessionManager.getOrCreateSession(mockUser);
      
      // Manually expire the session
      const session = sessionManager.sessions.get(mockUser.id);
      session.lastActivity = new Date(Date.now() - 31 * 60 * 1000); // 31 minutes ago

      const result = await sessionManager.getOrCreateSession(mockUser);

      expect(result.isExpired).toBe(true);
    });

    it('should parse session context from database', async () => {
      const userWithContext = {
        ...mockUser,
        session_context: JSON.stringify({ test: 'value' })
      };

      const session = await sessionManager.getOrCreateSession(userWithContext);

      expect(session.context).toEqual({ test: 'value' });
    });

    it('should handle invalid JSON context', async () => {
      const userWithBadContext = {
        ...mockUser,
        session_context: 'invalid json'
      };

      const session = await sessionManager.getOrCreateSession(userWithBadContext);

      expect(session.context).toEqual({});
      expect(logger.warn).toHaveBeenCalledWith(
        'Failed to parse session context',
        expect.any(Object)
      );
    });
  });

  describe('updateSession', () => {
    it('should update existing session', async () => {
      await sessionManager.getOrCreateSession(mockUser);
      
      const updatedData = {
        context: { newData: 'test' },
        state: STATES.HABIT_SETUP_NAME
      };

      const result = await sessionManager.updateSession(mockUser.id, updatedData);

      expect(result).toBe(true);
      const session = sessionManager.sessions.get(mockUser.id);
      expect(session.context).toEqual({ newData: 'test' });
      expect(session.state).toBe(STATES.HABIT_SETUP_NAME);
    });

    it('should update lastActivity timestamp', async () => {
      await sessionManager.getOrCreateSession(mockUser);
      const originalTime = sessionManager.sessions.get(mockUser.id).lastActivity;

      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 10));

      await sessionManager.updateSession(mockUser.id, { context: {} });

      const newTime = sessionManager.sessions.get(mockUser.id).lastActivity;
      expect(newTime.getTime()).toBeGreaterThan(originalTime.getTime());
    });

    it('should return false for non-existent session', async () => {
      const result = await sessionManager.updateSession(999, { context: {} });

      expect(result).toBe(false);
    });

    it('should merge context updates', async () => {
      await sessionManager.getOrCreateSession({
        ...mockUser,
        session_context: JSON.stringify({ existing: 'data' })
      });

      await sessionManager.updateSession(mockUser.id, {
        context: { new: 'value' }
      });

      const session = sessionManager.sessions.get(mockUser.id);
      expect(session.context).toEqual({
        existing: 'data',
        new: 'value'
      });
    });
  });

  describe('resetSession', () => {
    it('should reset session to main menu', async () => {
      await sessionManager.getOrCreateSession({
        ...mockUser,
        current_state: STATES.HABIT_SETUP_NAME,
        session_context: JSON.stringify({ temp: 'data' })
      });

      await sessionManager.resetSession(mockUser.id);

      const session = sessionManager.sessions.get(mockUser.id);
      expect(session.state).toBe(STATES.MAIN_MENU);
      expect(session.context).toEqual({});
      expect(User.updateState).toHaveBeenCalledWith(
        mockUser.id,
        STATES.MAIN_MENU,
        {}
      );
    });

    it('should handle non-existent session', async () => {
      await sessionManager.resetSession(999);

      expect(logger.warn).toHaveBeenCalledWith(
        'Attempted to reset non-existent session',
        { userId: 999 }
      );
    });
  });

  describe('isSessionExpired', () => {
    it('should detect expired session', () => {
      const oldDate = new Date(Date.now() - 31 * 60 * 1000); // 31 minutes ago
      const result = sessionManager.isSessionExpired(oldDate);

      expect(result).toBe(true);
    });

    it('should detect active session', () => {
      const recentDate = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago
      const result = sessionManager.isSessionExpired(recentDate);

      expect(result).toBe(false);
    });

    it('should handle edge case at exactly timeout', () => {
      const exactTimeout = new Date(Date.now() - 30 * 60 * 1000); // Exactly 30 minutes
      const result = sessionManager.isSessionExpired(exactTimeout);

      expect(result).toBe(true);
    });
  });

  describe('clearExpiredSessions', () => {
    it('should remove expired sessions', async () => {
      // Create multiple sessions
      await sessionManager.getOrCreateSession(mockUser);
      await sessionManager.getOrCreateSession({ ...mockUser, id: 2 });
      await sessionManager.getOrCreateSession({ ...mockUser, id: 3 });

      // Expire one session
      const session2 = sessionManager.sessions.get(2);
      session2.lastActivity = new Date(Date.now() - 35 * 60 * 1000);

      sessionManager.clearExpiredSessions();

      expect(sessionManager.sessions.size).toBe(2);
      expect(sessionManager.sessions.has(2)).toBe(false);
    });

    it('should log cleared sessions', () => {
      sessionManager.sessions.set(1, {
        lastActivity: new Date(Date.now() - 40 * 60 * 1000)
      });

      sessionManager.clearExpiredSessions();

      expect(logger.info).toHaveBeenCalledWith(
        'Cleared expired sessions',
        { count: 1 }
      );
    });
  });

  describe('getSession', () => {
    it('should return existing session', async () => {
      await sessionManager.getOrCreateSession(mockUser);
      
      const session = sessionManager.getSession(mockUser.id);

      expect(session).toBeDefined();
      expect(session.user.id).toBe(mockUser.id);
    });

    it('should return null for non-existent session', () => {
      const session = sessionManager.getSession(999);

      expect(session).toBeNull();
    });
  });

  describe('checkRateLimit', () => {
    it('should track message count', async () => {
      await sessionManager.getOrCreateSession(mockUser);

      const result1 = await sessionManager.checkRateLimit(mockUser.id);
      expect(result1.limited).toBe(false);
      expect(result1.remaining).toBeGreaterThan(0);

      // Simulate multiple messages
      const session = sessionManager.sessions.get(mockUser.id);
      session.messageCount = 99;

      const result2 = await sessionManager.checkRateLimit(mockUser.id);
      expect(result2.remaining).toBe(0);
    });

    it('should enforce rate limit', async () => {
      await sessionManager.getOrCreateSession(mockUser);
      
      const session = sessionManager.sessions.get(mockUser.id);
      session.messageCount = 100;

      const result = await sessionManager.checkRateLimit(mockUser.id);

      expect(result.limited).toBe(true);
      expect(result.remaining).toBe(0);
    });

    it('should reset rate limit after window', async () => {
      await sessionManager.getOrCreateSession(mockUser);
      
      const session = sessionManager.sessions.get(mockUser.id);
      session.messageCount = 100;
      session.rateLimitReset = new Date(Date.now() - 1000); // Past reset time

      const result = await sessionManager.checkRateLimit(mockUser.id);

      expect(result.limited).toBe(false);
      expect(session.messageCount).toBe(1);
    });

    it('should handle missing session', async () => {
      const result = await sessionManager.checkRateLimit(999);

      expect(result.limited).toBe(false);
      expect(result.remaining).toBe(100);
    });
  });

  describe('getAllSessions', () => {
    it('should return all active sessions', async () => {
      await sessionManager.getOrCreateSession(mockUser);
      await sessionManager.getOrCreateSession({ ...mockUser, id: 2 });

      const sessions = sessionManager.getAllSessions();

      expect(sessions.length).toBe(2);
      expect(sessions[0]).toHaveProperty('userId');
      expect(sessions[0]).toHaveProperty('lastActivity');
    });

    it('should return empty array when no sessions', () => {
      const sessions = sessionManager.getAllSessions();

      expect(sessions).toEqual([]);
    });
  });

  describe('cleanupInactiveSessions', () => {
    it('should reset inactive user sessions', async () => {
      User.getInactiveSessions.mockResolvedValue([
        { id: 1, current_state: STATES.HABIT_SETUP_NAME },
        { id: 2, current_state: STATES.LOG_HABIT_SELECT }
      ]);

      await sessionManager.cleanupInactiveSessions();

      expect(User.getInactiveSessions).toHaveBeenCalledWith(30 * 60 * 1000);
      expect(User.updateState).toHaveBeenCalledTimes(2);
      expect(User.updateState).toHaveBeenCalledWith(1, STATES.MAIN_MENU, {});
      expect(User.updateState).toHaveBeenCalledWith(2, STATES.MAIN_MENU, {});
    });

    it('should handle cleanup errors', async () => {
      User.getInactiveSessions.mockRejectedValue(new Error('Database error'));

      await sessionManager.cleanupInactiveSessions();

      expect(logger.error).toHaveBeenCalledWith(
        'Error cleaning up inactive sessions',
        expect.objectContaining({ error: 'Database error' })
      );
    });
  });
});