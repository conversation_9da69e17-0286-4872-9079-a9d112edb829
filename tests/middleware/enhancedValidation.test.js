const {
  validateWebhook,
  validatePhone,
  validatePaymentData,
  sanitizeInput,
  sanitizeEmail,
  sanitizeURL,
  sanitizeNumber,
  safeSQLIdentifier
} = require('../../src/middleware/enhancedValidation');
const Joi = require('joi');
const validator = require('validator');
const DOMPurify = require('isomorphic-dompurify');
const logger = require('../../src/config/logger');

// Don't mock Joi - we need the real implementation
jest.mock('validator');
jest.mock('isomorphic-dompurify');
jest.mock('../../src/config/logger');

describe('Enhanced Validation Middleware', () => {
  let mockReq, mockRes, mockNext;

  beforeEach(() => {
    jest.clearAllMocks();
    mockReq = { body: {} };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };
    mockNext = jest.fn();
  });

  describe('sanitizeInput', () => {
    it('should sanitize clean input', () => {
      validator.escape.mockReturnValue('clean text');
      DOMPurify.sanitize.mockReturnValue('clean text');

      const result = sanitizeInput('  clean text  ');

      expect(DOMPurify.sanitize).toHaveBeenCalledWith('clean text', expect.any(Object));
      expect(validator.escape).toHaveBeenCalledWith('clean text');
      expect(result).toBe('clean text');
    });

    it('should remove SQL injection patterns', () => {
      DOMPurify.sanitize.mockImplementation(text => text);
      validator.escape.mockImplementation(text => text);
      logger.warn.mockImplementation();

      const result = sanitizeInput('DROP TABLE users; --');

      expect(logger.warn).toHaveBeenCalledWith('SQL injection attempt detected and blocked', expect.any(Object));
      expect(result).toBe(' TABLE users;'); // Pattern removed
    });

    it('should remove script tags and javascript', () => {
      DOMPurify.sanitize.mockImplementation(text => text.replace(/<script>.*?<\/script>/gi, ''));
      validator.escape.mockImplementation(text => text);
      logger.warn.mockImplementation();

      const result = sanitizeInput('<script>alert("xss")</script><a href="javascript:alert(1)">click</a>');

      expect(logger.warn).toHaveBeenCalledWith('Script injection attempt detected', expect.any(Object));
      expect(result).toBe('alert("xss")click'); // Scripts removed, but additional cleaning applied
    });

    it('should normalize whitespace and limit length', () => {
      DOMPurify.sanitize.mockImplementation(text => text);
      validator.escape.mockImplementation(text => text);

      const longText = 'a'.repeat(2000);
      const result = sanitizeInput(`  ${longText}   multiple   spaces  `);

      expect(result).toHaveLength(1000); // Limited to 1000 chars
      expect(result).toBe('a'.repeat(1000)); // Whitespace normalized, trimmed
    });

    it('should handle empty input', () => {
      expect(sanitizeInput('')).toBe('');
      expect(sanitizeInput(null)).toBe('');
      expect(sanitizeInput(undefined)).toBe('');
    });
  });

  describe('sanitizeEmail', () => {
    it('should normalize valid email', () => {
      validator.isEmail.mockReturnValue(true);
      validator.normalizeEmail.mockReturnValue('<EMAIL>');

      const result = sanitizeEmail(' <EMAIL> ');

      expect(validator.isEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(validator.normalizeEmail).toHaveBeenCalledWith('<EMAIL>', expect.any(Object));
      expect(result).toBe('<EMAIL>');
    });

    it('should return empty for invalid email', () => {
      validator.isEmail.mockReturnValue(false);
      logger.warn.mockImplementation();

      const result = sanitizeEmail('invalid-email');

      expect(validator.isEmail).toHaveBeenCalledWith('invalid-email');
      expect(logger.warn).toHaveBeenCalledWith('Invalid email format', { email: 'invalid-email' });
      expect(result).toBe('');
    });

    it('should handle empty input', () => {
      expect(sanitizeEmail('')).toBe('');
      expect(sanitizeEmail(null)).toBe('');
    });
  });

  describe('sanitizeURL', () => {
    it('should validate and return valid URL', () => {
      validator.isURL.mockReturnValue(true);

      const result = sanitizeURL('  https://example.com/path?query=1  ');

      expect(validator.isURL).toHaveBeenCalledWith('https://example.com/path?query=1', expect.any(Object));
      expect(result).toBe('https://example.com/path?query=1');
    });

    it('should return empty for invalid URL', () => {
      validator.isURL.mockReturnValue(false);
      logger.warn.mockImplementation();

      const result = sanitizeURL('invalid-url');

      expect(validator.isURL).toHaveBeenCalledWith('invalid-url', expect.any(Object));
      expect(logger.warn).toHaveBeenCalledWith('Invalid URL format', { url: 'invalid-url' });
      expect(result).toBe('');
    });

    it('should handle empty input', () => {
      expect(sanitizeURL('')).toBe('');
      expect(sanitizeURL(null)).toBe('');
    });
  });

  describe('sanitizeNumber', () => {
    it('should sanitize integer within range', () => {
      validator.isInt.mockReturnValue(true);

      expect(sanitizeNumber('30')).toBe(30);
      expect(sanitizeNumber(30)).toBe(30);
      expect(sanitizeNumber('30.5')).toBe(null); // Not int
    });

    it('should sanitize float when allowed', () => {
      validator.isFloat.mockReturnValue(true);

      expect(sanitizeNumber('30.5', { allowFloat: true })).toBe(30.5);
    });

    it('should clamp to min/max', () => {
      validator.isInt.mockReturnValue(true);

      expect(sanitizeNumber('-5', { min: 0 })).toBe(0);
      expect(sanitizeNumber('1000', { max: 100 })).toBe(100);
    });

    it('should return null for invalid', () => {
      validator.isInt.mockReturnValue(false);

      expect(sanitizeNumber('invalid')).toBeNull();
      expect(sanitizeNumber(null)).toBeNull();
    });
  });

  describe('safeSQLIdentifier', () => {
    it('should clean valid identifier', () => {
      expect(safeSQLIdentifier('valid_column')).toBe('valid_column');
      expect(safeSQLIdentifier('_starts_with_underscore')).toBe('_starts_with_underscore');
      expect(safeSQLIdentifier('User123')).toBe('User123');
    });

    it('should remove invalid characters', () => {
      expect(safeSQLIdentifier('invalid; DROP')).toBe('invalid');
      expect(safeSQLIdentifier('space test')).toBe('spacetest');
      expect(safeSQLIdentifier('1starts_with_number')).toBe(''); // Must start with letter/underscore
    });

    it('should limit length', () => {
      const long = 'a'.repeat(70);
      expect(safeSQLIdentifier(long)).toHaveLength(63);
    });

    it('should handle empty', () => {
      expect(safeSQLIdentifier('')).toBe('');
      expect(safeSQLIdentifier(null)).toBe('');
    });
  });

  describe('validatePhone', () => {
    it('should validate E.164 phone', () => {
      const mockJoi = { validate: jest.fn().mockReturnValue({ error: null }) };
      const mockIsMobilePhone = jest.fn().mockReturnValue(true);
      validator.isMobilePhone = mockIsMobilePhone;
      Joi.string = jest.fn().mockReturnValue({
        pattern: jest.fn().mockReturnThis(),
        required: jest.fn().mockReturnValue(mockJoi)
      });
      Joi.object = jest.fn().mockReturnValue({ validate: jest.fn().mockReturnValue({ error: null }) });

      expect(validatePhone('+1234567890')).toBe(true);
      expect(mockIsMobilePhone).toHaveBeenCalledWith('1234567890', 'any', { strictMode: false });
    });

    it('should validate with whatsapp prefix', () => {
      const mockJoi = { validate: jest.fn().mockReturnValue({ error: null }) };
      validator.isMobilePhone = jest.fn().mockReturnValue(true);
      Joi.string = jest.fn().mockReturnValue({
        pattern: jest.fn().mockReturnThis(),
        required: jest.fn().mockReturnValue(mockJoi)
      });

      expect(validatePhone('whatsapp:+1234567890')).toBe(true);
    });

    it('should reject invalid phone', () => {
      const mockJoiError = { validate: jest.fn().mockReturnValue({ error: { details: ['error'] } }) };
      Joi.string = jest.fn().mockReturnValue({
        pattern: jest.fn().mockReturnThis(),
        required: jest.fn().mockReturnValue(mockJoiError)
      });

      expect(validatePhone('invalid')).toBe(false);
    });

    it('should reject empty', () => {
      expect(validatePhone('')).toBe(false);
      expect(validatePhone(null)).toBe(false);
    });
  });

  describe('validatePaymentData', () => {
    it('should validate valid payment data', () => {
      const mockData = {
        event: 'order.completed',
        customer_email: '<EMAIL>',
        order_id: 'order123',
        order_total: 50.00,
        currency: 'USD'
      };
      const mockJoi = { validate: jest.fn().mockReturnValue({ error: null, value: mockData }) };
      Joi.object = jest.fn().mockReturnValue(mockJoi);
      sanitizeEmail.mockReturnValue('<EMAIL>');

      const result = validatePaymentData(mockData);

      expect(result).toEqual(mockData);
      expect(sanitizeEmail).toHaveBeenCalledWith('<EMAIL>');
    });

    it('should reject invalid data', () => {
      const mockData = { event: 'invalid' };
      const mockJoi = { validate: jest.fn().mockReturnValue({ error: { details: ['error'] } }) };
      Joi.object = jest.fn().mockReturnValue(mockJoi);
      logger.warn.mockImplementation();

      const result = validatePaymentData(mockData);

      expect(logger.warn).toHaveBeenCalledWith('Invalid payment data', { error: 'error' });
      expect(result).toBeNull();
    });

    it('should sanitize fields in valid data', () => {
      const mockData = {
        event: 'order.completed',
        customer_email: ' <EMAIL> ',
        customer_name: '<script>alert(1)</script>',
        product_name: 'Test Product'
      };
      const mockJoi = { validate: jest.fn().mockReturnValue({ error: null, value: mockData }) };
      Joi.object = jest.fn().mockReturnValue(mockJoi);
      sanitizeEmail.mockReturnValue('<EMAIL>');
      sanitizeInput.mockImplementation(text => text.replace(/<script>.*?<\/script>/gi, ''));

      const result = validatePaymentData(mockData);

      expect(sanitizeEmail).toHaveBeenCalledWith(' <EMAIL> ');
      expect(sanitizeInput).toHaveBeenCalledWith('<script>alert(1)</script>');
      expect(result.customer_email).toBe('<EMAIL>');
      expect(result.customer_name).toBe('alert(1)'); // Sanitized
    });
  });

  describe('validateWebhook', () => {
    it('should call next on valid webhook', async () => {
      const mockJoi = { validate: jest.fn().mockReturnValue({ error: null, value: mockReq.body }) };
      Joi.object = jest.fn().mockReturnValue(mockJoi);
      sanitizeInput.mockReturnValue('sanitized body');

      mockReq.body = {
        Body: 'test message',
        From: '+1234567890',
        MessageSid: 'sid123'
      };

      await validateWebhook(mockReq, mockRes, mockNext);

      expect(mockJoi.validate).toHaveBeenCalledWith(mockReq.body);
      expect(sanitizeInput).toHaveBeenCalledWith('test message');
      expect(mockReq.body.Body).toBe('sanitized body');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should return 400 on validation error', async () => {
      const mockJoi = { validate: jest.fn().mockReturnValue({ error: { details: ['Invalid Body'] } }) };
      Joi.object = jest.fn().mockReturnValue(mockJoi);
      logger.warn.mockImplementation();

      mockReq.body = { From: 'invalid' };
      mockReq.ip = '127.0.0.1';
      mockReq.path = '/webhook';

      await validateWebhook(mockReq, mockRes, mockNext);

      expect(mockJoi.validate).toHaveBeenCalledWith(mockReq.body);
      expect(logger.warn).toHaveBeenCalledWith('Invalid webhook request', { 
        error: 'Invalid Body', 
        ip: '127.0.0.1', 
        path: '/webhook' 
      });
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.send).toHaveBeenCalledWith('Invalid request');
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should sanitize ProfileName if present', async () => {
      const mockJoi = { validate: jest.fn().mockReturnValue({ error: null, value: mockReq.body }) };
      Joi.object = jest.fn().mockReturnValue(mockJoi);
      sanitizeInput.mockReturnValue('sanitized');

      mockReq.body = {
        Body: 'test',
        From: '+1234567890',
        ProfileName: '<script>evil</script>'
      };

      await validateWebhook(mockReq, mockRes, mockNext);

      expect(sanitizeInput).toHaveBeenCalledWith('test'); // Body
      expect(sanitizeInput).toHaveBeenCalledWith('<script>evil</script>'); // ProfileName
      expect(mockReq.body.ProfileName).toBe('sanitized');
      expect(mockNext).toHaveBeenCalled();
    });
  });
});