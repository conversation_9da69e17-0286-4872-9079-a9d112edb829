const request = require('supertest');
const express = require('express');
const webhookController = require('../../src/controllers/webhookControllerCompliant');
const User = require('../../src/models/User');
const sessionManager = require('../../src/services/sessionManager');
const unifiedStateMachine = require('../../src/services/unifiedStateMachine');
const complianceService = require('../../src/services/complianceService');
const auditLog = require('../../src/services/complianceAuditService');
const logger = require('../../src/config/logger');

jest.mock('../../src/models/User');
jest.mock('../../src/services/sessionManager');
jest.mock('../../src/services/unifiedStateMachine');
jest.mock('../../src/services/complianceService');
jest.mock('../../src/services/complianceAuditService');
jest.mock('../../src/config/logger');

describe('webhookControllerCompliant', () => {
  let app;

  beforeEach(() => {
    jest.clearAllMocks();
    app = express();
    app.use(express.urlencoded({ extended: false }));
    app.post('/webhook', webhookController.handleIncomingMessage.bind(webhookController));
    app.get('/webhook/status', webhookController.getStatus ? webhookController.getStatus.bind(webhookController) : (req, res) => res.json({ status: 'operational' }));
  });

  describe('handleMessage', () => {
    const mockUser = {
      id: 1,
      phone: '+**********',
      status: 'ACTIVE',
      current_state: 'MAIN_MENU',
      session_context: {}
    };

    it('should handle STOP command for opt-out', async () => {
      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'STOP'
        });

      expect(response.status).toBe(200);
      expect(complianceService.handleOptOut).toHaveBeenCalledWith('+**********');
      expect(auditLog.log).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'user_opted_out'
        })
      );
    });

    it('should handle START command for opt-in', async () => {
      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'START'
        });

      expect(response.status).toBe(200);
      expect(complianceService.handleOptIn).toHaveBeenCalledWith('+**********');
      expect(auditLog.log).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'user_opted_in'
        })
      );
    });

    it('should handle regular message for active user', async () => {
      User.findOrCreate.mockResolvedValue(mockUser);
      sessionManager.getOrCreateSession.mockResolvedValue({
        user: mockUser,
        context: {}
      });
      unifiedStateMachine.handleMessage.mockResolvedValue({
        response: 'Test response',
        user: mockUser
      });

      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'Hello'
        });

      expect(response.status).toBe(200);
      expect(response.text).toContain('Test response');
      expect(User.findOrCreate).toHaveBeenCalledWith('+**********');
      expect(sessionManager.getOrCreateSession).toHaveBeenCalledWith(mockUser);
      expect(unifiedStateMachine.handleMessage).toHaveBeenCalledWith(
        mockUser,
        'Hello',
        expect.any(Object)
      );
    });

    it('should handle data request command', async () => {
      User.findOrCreate.mockResolvedValue(mockUser);
      complianceService.handleDataRequest.mockResolvedValue({
        success: true,
        message: 'Data sent to email'
      });

      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'DATA REQUEST'
        });

      expect(response.status).toBe(200);
      expect(complianceService.handleDataRequest).toHaveBeenCalledWith(mockUser);
      expect(response.text).toContain('Data sent to email');
    });

    it('should handle delete request command', async () => {
      User.findOrCreate.mockResolvedValue(mockUser);
      complianceService.handleDeleteRequest.mockResolvedValue({
        success: true,
        message: 'Data deleted'
      });

      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'DELETE ACCOUNT'
        });

      expect(response.status).toBe(200);
      expect(complianceService.handleDeleteRequest).toHaveBeenCalledWith(mockUser);
      expect(response.text).toContain('Data deleted');
    });

    it('should handle paused user', async () => {
      const pausedUser = { ...mockUser, status: 'PAUSED' };
      User.findOrCreate.mockResolvedValue(pausedUser);

      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'Hello'
        });

      expect(response.status).toBe(200);
      expect(response.text).toContain('opted out');
      expect(unifiedStateMachine.handleMessage).not.toHaveBeenCalled();
    });

    it('should handle session timeout', async () => {
      User.findOrCreate.mockResolvedValue(mockUser);
      sessionManager.getOrCreateSession.mockResolvedValue({
        user: mockUser,
        context: {},
        isExpired: true
      });

      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'Hello'
        });

      expect(response.status).toBe(200);
      expect(sessionManager.resetSession).toHaveBeenCalledWith(mockUser.id);
    });

    it('should handle errors gracefully', async () => {
      User.findOrCreate.mockRejectedValue(new Error('Database error'));

      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'Hello'
        });

      expect(response.status).toBe(500);
      expect(logger.error).toHaveBeenCalledWith(
        'Error in webhook handler',
        expect.objectContaining({ error: 'Database error' })
      );
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/webhook')
        .send({});

      expect(response.status).toBe(400);
      expect(response.text).toContain('Missing required fields');
    });

    it('should handle age verification failure', async () => {
      User.findOrCreate.mockResolvedValue({
        ...mockUser,
        age_verified: false
      });

      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'Hello'
        });

      expect(response.status).toBe(200);
      expect(response.text).toContain('age verification');
    });

    it('should track message metrics', async () => {
      User.findOrCreate.mockResolvedValue(mockUser);
      sessionManager.getOrCreateSession.mockResolvedValue({
        user: mockUser,
        context: {}
      });
      unifiedStateMachine.handleMessage.mockResolvedValue({
        response: 'Test response',
        user: mockUser
      });

      await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'Hello'
        });

      expect(User.updateLastActive).toHaveBeenCalledWith(mockUser.id);
      expect(auditLog.log).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'message_received',
          userId: mockUser.id
        })
      );
    });
  });

  describe('getStatus', () => {
    it('should return webhook status', async () => {
      const response = await request(app)
        .get('/webhook/status');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'operational');
      expect(response.body).toHaveProperty('version');
      expect(response.body).toHaveProperty('compliance');
    });
  });

  describe('rate limiting', () => {
    it('should handle rate limit exceeded', async () => {
      User.findOrCreate.mockResolvedValue({
        id: 1,
        phone: '+**********',
        status: 'ACTIVE'
      });

      // Simulate rate limit by setting session manager to return rate limited
      sessionManager.checkRateLimit.mockResolvedValue({
        limited: true,
        remaining: 0
      });

      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'Hello'
        });

      expect(response.status).toBe(429);
      expect(response.text).toContain('Rate limit exceeded');
    });
  });

  describe('consent handling', () => {
    it('should check consent before processing', async () => {
      const userWithoutConsent = {
        id: 1,
        phone: '+**********',
        status: 'ACTIVE',
        consent_given: false
      };
      
      User.findOrCreate.mockResolvedValue(userWithoutConsent);

      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'Hello'
        });

      expect(response.status).toBe(200);
      expect(response.text).toContain('consent');
      expect(unifiedStateMachine.handleMessage).not.toHaveBeenCalled();
    });

    it('should process consent acceptance', async () => {
      const userWithoutConsent = {
        id: 1,
        phone: '+**********',
        status: 'ACTIVE',
        consent_given: false
      };
      
      User.findOrCreate.mockResolvedValue(userWithoutConsent);
      complianceService.recordConsent.mockResolvedValue({
        ...userWithoutConsent,
        consent_given: true
      });

      const response = await request(app)
        .post('/webhook')
        .send({
          From: 'whatsapp:+**********',
          Body: 'YES'
        });

      expect(response.status).toBe(200);
      expect(complianceService.recordConsent).toHaveBeenCalledWith(
        userWithoutConsent,
        true
      );
    });
  });
});