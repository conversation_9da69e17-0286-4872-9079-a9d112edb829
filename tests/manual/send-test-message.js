require('dotenv').config();
const twilio = require('twilio');

// Initialize Twilio client
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const twilioNumber = process.env.TWILIO_PHONE_NUMBER;

if (!accountSid || !authToken || !twilioNumber) {
  console.error('Missing Twilio credentials in environment variables');
  process.exit(1);
}

const client = twilio(accountSid, authToken);

// Test user details
const testUserPhone = 'whatsapp:+************'; // Max <PERSON>
const testMessage = `🎯 *HABIT TRACKER TEST MESSAGE*

Hello Max Gibson! This is a test message from your Habit Tracker system.

✅ System Status: OPERATIONAL
📱 WhatsApp Integration: WORKING
🔗 Connection: SUCCESSFUL

If you received this message, the messaging system is working correctly!

Reply with "<PERSON><PERSON>" to interact with the bot or "TEST" to confirm receipt.

Timestamp: ${new Date().toISOString()}`;

async function sendTestMessage() {
  try {
    console.log('Sending test message to:', testUserPhone);
    console.log('From:', twilioNumber);
    console.log('Using Account SID:', accountSid.substring(0, 10) + '...');
    
    const message = await client.messages.create({
      body: testMessage,
      from: `whatsapp:${twilioNumber}`,
      to: testUserPhone
    });
    
    console.log('\n✅ SUCCESS! Message sent successfully');
    console.log('Message SID:', message.sid);
    console.log('Status:', message.status);
    console.log('Date created:', message.dateCreated);
    console.log('Direction:', message.direction);
    console.log('Price:', message.price, message.priceUnit);
    
    // Additional details
    if (message.errorCode) {
      console.log('Error code:', message.errorCode);
      console.log('Error message:', message.errorMessage);
    }
    
    console.log('\n📊 Message Details:');
    console.log('- To:', message.to);
    console.log('- From:', message.from);
    console.log('- Body length:', message.body.length, 'characters');
    
    return message;
  } catch (error) {
    console.error('\n❌ ERROR sending message:');
    console.error('Error code:', error.code);
    console.error('Error message:', error.message);
    
    if (error.moreInfo) {
      console.error('More info:', error.moreInfo);
    }
    
    if (error.details) {
      console.error('Details:', JSON.stringify(error.details, null, 2));
    }
    
    process.exit(1);
  }
}

// Run the test
console.log('='.repeat(60));
console.log('HABIT TRACKER - WHATSAPP MESSAGE TEST');
console.log('='.repeat(60));
console.log('Environment:', process.env.NODE_ENV || 'development');
console.log('Timestamp:', new Date().toISOString());
console.log('='.repeat(60));
console.log('');

sendTestMessage()
  .then(() => {
    console.log('\n' + '='.repeat(60));
    console.log('Test completed successfully!');
    console.log('='.repeat(60));
    process.exit(0);
  })
  .catch((error) => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });