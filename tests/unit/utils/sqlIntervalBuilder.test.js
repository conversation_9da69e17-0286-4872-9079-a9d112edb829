const SqlIntervalBuilder = require('../../../src/utils/sqlIntervalBuilder').SqlIntervalBuilder;
const logger = require('../../../src/config/logger');

jest.mock('../../../src/config/logger');

describe('SqlIntervalBuilder', () => {
  let builder;

  beforeEach(() => {
    jest.clearAllMocks();
    builder = new SqlIntervalBuilder();
  });

  describe('buildSafeInterval', () => {
    it('should build safe interval string for valid input', () => {
      expect(builder.buildSafeInterval(30, 'day')).toBe('30 days');
      expect(builder.buildSafeInterval(1, 'day')).toBe('1 day'); // Singular
      expect(builder.buildSafeInterval(2, 'days')).toBe('2 days'); // <PERSON>les plural input
      expect(builder.buildSafeInterval(5, 'hour')).toBe('5 hours');
      expect(builder.buildSafeInterval(1, 'minute')).toBe('1 minute');
    });

    it('should throw error for invalid value', () => {
      expect(() => builder.buildSafeInterval(-1, 'day')).toThrow('Invalid interval value: -1');
      expect(() => builder.buildSafeInterval('invalid', 'day')).toThrow('Invalid interval value: invalid');
      expect(() => builder.buildSafeInterval(NaN, 'day')).toThrow('Invalid interval value: NaN');
    });

    it('should throw error for invalid unit', () => {
      expect(() => builder.buildSafeInterval(30, 'invalid')).toThrow('Invalid interval unit: invalid. Valid units are: second, minute, hour, day, week, month, year');
      // 'Day' with capital D should NOT throw since implementation normalizes to lowercase
      expect(builder.buildSafeInterval(30, 'Day')).toBe('30 days');
    });
  });

  describe('buildMakeInterval', () => {
    it('should build parameterized make_interval for valid units', () => {
      expect(builder.buildMakeInterval(30, 'day')).toEqual({
        query: 'make_interval(days => $1)',
        value: 30
      });
      expect(builder.buildMakeInterval(1, 'year')).toEqual({
        query: 'make_interval(years => $1)',
        value: 1
      });
      expect(builder.buildMakeInterval(5, 'minute')).toEqual({
        query: 'make_interval(mins => $1)',
        value: 5
      });
      expect(builder.buildMakeInterval(10, 'second')).toEqual({
        query: 'make_interval(secs => $1)',
        value: 10
      });
    });

    it('should handle plural unit input', () => {
      expect(builder.buildMakeInterval(2, 'days')).toEqual({
        query: 'make_interval(days => $1)',
        value: 2
      });
    });

    it('should throw error for invalid value', () => {
      expect(() => builder.buildMakeInterval(-1, 'day')).toThrow('Invalid interval value: -1');
      expect(() => builder.buildMakeInterval('invalid', 'day')).toThrow('Invalid interval value: invalid');
    });

    it('should throw error for invalid unit', () => {
      expect(() => builder.buildMakeInterval(30, 'invalid')).toThrow('Invalid interval unit for make_interval: invalid');
    });
  });

  describe('parseRetentionPolicy', () => {
    it('should parse valid policy strings', () => {
      expect(builder.parseRetentionPolicy('30 days')).toEqual({
        query: 'make_interval(days => $1)',
        value: 30
      });
      expect(builder.parseRetentionPolicy('1 year')).toEqual({
        query: 'make_interval(years => $1)',
        value: 1
      });
      expect(builder.parseRetentionPolicy('6 months')).toEqual({
        query: 'make_interval(months => $1)',
        value: 6
      });
    });

    it('should throw error for invalid format', () => {
      expect(() => builder.parseRetentionPolicy('invalid')).toThrow('Invalid retention policy format: invalid');
      expect(() => builder.parseRetentionPolicy('30')).toThrow('Invalid retention policy format: 30');
      expect(() => builder.parseRetentionPolicy('days 30')).toThrow('Invalid retention policy format: days 30');
    });
  });

  describe('buildDateComparison', () => {
    it('should build valid date comparison query', () => {
      const result = builder.buildDateComparison('created_at', '<', 30, 'day');
      expect(result.query).toBe('created_at < NOW() - make_interval(days => $1)');
      expect(result.value).toBe(30);
    });

    it('should validate column name', () => {
      expect(() => builder.buildDateComparison('invalid; DROP TABLE', '<', 30, 'day')).toThrow('Invalid column name: invalid; DROP TABLE');
      expect(() => builder.buildDateComparison('valid_column', '<', 30, 'day')).not.toThrow();
    });

    it('should validate operator', () => {
      expect(builder.buildDateComparison('created_at', '<', 30, 'day')).toEqual(
        expect.objectContaining({ query: expect.stringContaining('<') })
      );
      expect(() => builder.buildDateComparison('created_at', 'invalid', 30, 'day')).toThrow('Invalid operator: invalid');
      // Valid operators
      ['<', '>', '<=', '>=', '=', '!='].forEach(op => {
        expect(() => builder.buildDateComparison('created_at', op, 30, 'day')).not.toThrow();
      });
    });

    it('should propagate interval errors', () => {
      expect(() => builder.buildDateComparison('created_at', '<', -1, 'day')).toThrow('Invalid interval value: -1');
      expect(() => builder.buildDateComparison('created_at', '<', 30, 'invalid')).toThrow('Invalid interval unit for make_interval: invalid');
    });
  });

  describe('migrateIntervalQuery', () => {
    it('should migrate simple INTERVAL to parameterized', () => {
      const oldQuery = "SELECT * FROM logs WHERE created_at > NOW() - INTERVAL '30 days'";
      const result = builder.migrateIntervalQuery(oldQuery);

      expect(result.query).toBe("SELECT * FROM logs WHERE created_at > NOW() - make_interval(days => $1)");
      expect(result.parameters).toEqual([30]);
    });

    it('should migrate multiple intervals', () => {
      const oldQuery = "SELECT * FROM logs WHERE created_at > NOW() - INTERVAL '7 days' AND updated_at < NOW() - INTERVAL '1 hour'";
      const result = builder.migrateIntervalQuery(oldQuery);

      expect(result.query).toBe("SELECT * FROM logs WHERE created_at > NOW() - make_interval(days => $1) AND updated_at < NOW() - make_interval(hours => $2)");
      expect(result.parameters).toEqual([7, 1]);
    });

    it('should keep original on migration failure', () => {
      jest.spyOn(logger, 'warn');
      // Use a pattern that matches the regex but has invalid unit
      const oldQuery = "SELECT * FROM logs WHERE created_at > NOW() - INTERVAL '30 invalidunit'";
      const result = builder.migrateIntervalQuery(oldQuery);

      expect(result.query).toBe("SELECT * FROM logs WHERE created_at > NOW() - INTERVAL '30 invalidunit'");
      expect(result.parameters).toEqual([]);
      expect(logger.warn).toHaveBeenCalledWith('Failed to migrate interval in query', expect.any(Object));
    });

    it('should handle no intervals', () => {
      const oldQuery = "SELECT * FROM logs WHERE status = 'active'";
      const result = builder.migrateIntervalQuery(oldQuery);

      expect(result.query).toBe("SELECT * FROM logs WHERE status = 'active'");
      expect(result.parameters).toEqual([]);
    });
  });

  describe('isValidInterval', () => {
    it('should validate correct interval strings', () => {
      expect(builder.isValidInterval('30 days')).toBe(true);
      expect(builder.isValidInterval('1 year')).toBe(true);
      expect(builder.isValidInterval('5 hours')).toBe(true);
    });

    it('should reject invalid formats', () => {
      expect(builder.isValidInterval('invalid')).toBe(false);
      expect(builder.isValidInterval('30')).toBe(false);
      expect(builder.isValidInterval('days 30')).toBe(false);
      expect(builder.isValidInterval('30 invalid')).toBe(false);
      expect(builder.isValidInterval('')).toBe(false);
    });

    it('should reject invalid units even if format correct', () => {
      expect(builder.isValidInterval('30 invalidunit')).toBe(false);
    });
  });
});